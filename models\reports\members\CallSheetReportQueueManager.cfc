﻿<cfcomponent output="false" cache="true">
	<cfproperty name="objReportHelper" inject="reports.report">
	<cfproperty name="birtRunner" inject="reports.birtRunner">

	<cffunction name="runTask" access="public" output="false" returntype="void">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>

		<cfsetting requesttimeout="400">
		
		<cfscript>
			local.arrTaskFields = [ 
				{ name="BatchSize", type="INTEGER", desc="Batch Size", value="50" },
				{ name="Threads", type="INTEGER", desc="Number of Threads", value="4" } 
			];
			local.strTaskFields = arguments.strTask.scheduledTasksUtils.setTaskCustomFields(siteID=application.objSiteInfo.mc_siteinfo['MC'].siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>	

		<cfset local.messageTypeID = arguments.strTask.scheduledTasksUtils.getMessageTypeID(messageTypeCode="CALLSHEETRPT")>
		<cfset local.processQueueResult = processQueue(messageTypeID=local.messageTypeID, batchSize=local.strTaskFields.batchSize, threads=local.strTaskFields.threads)>
		<cfif NOT local.processQueueResult.success>
			<cfthrow message="Error running processQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier='', itemcount=local.processQueueResult.itemCount)>
		</cfif>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfargument name="messageTypeID" type="numeric" required="true">
		<cfargument name="batchSize" type="numeric" required="true">
		<cfargument name="threads" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset var reportdesign = "/app/models/reports/members/callsheetreport.rptdesign">
		<cfset var grabForProcessingStr = structnew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>

		<cftry>
			<cfset grabForProcessingStr = queueGrabForProcessing(batchsize=arguments.batchSize)>			
			<cfset local.qryMembers = grabForProcessingStr.qryGrabItems>
			<cfset local.returnStruct.itemCount = local.qryMembers.recordCount>
		
			<cfscript>
			// loop per member
			QueryEach(local.qryMembers, function(struct thisMember, numeric currentrow, query qryMembers) {

				var outputFolderName = "";
				var outputFileName = "";
				var reportXMLFile = "";
				var paramSet = arrayNew(1);
				var reportParams = arrayNew(1);
				var reportCreated = false;		
				var reportParamsStr = structNew();
				var memberPhotoUrl = "";

				// item must still be in the grabbedForProcessing state for this job. else skip it.
				// this is here because we have a check in place that will attempt to clear items "stuck" in the grabbedForProcessing state 
				// and those items may be grabbed by another job, causing it to possible be processed twice.
				if (queueItemHasStatus(queueType='callSheets', queueStatus='grabbedForProcessing', itemUID=thisMember.itemUID, memberID=thisMember.memberID)) {
					try {		

						// UPDATE STATUS
						queueSetStatus(queueType='callSheets', queueStatus='processingItem', itemUID=thisMember.itemUID, memberID=thisMember.memberID);
						
						// filename for pdf
						outputFileName = "#thisMember.itemUID#_#thisMember.memberID#";
						outputFileName = replace(replace(rereplaceNoCase(outputFileName,'[^A-Z0-9 ]','','ALL'),' ','_','ALL'),'__','_','ALL');
						outputFolderName = "#application.paths.SharedTemp.path#callsheet_#thisMember.itemUID#";
						outputFileName = "#outputFolderName#/#outputFileName#.pdf";
						memberPhotoUrl = "#replaceNoCase(application.paths.MCPlatform.internalUrl,'*SITECODE*',thisMember.siteCode)#userassets/#lcase(thisMember.orgCode)#/memberphotosth/";

						if (not directoryExists(outputFolderName)) 
							directoryCreate(outputFolderName,true,true);

						reportParamsStr = getBirtParams(itemUID=thisMember.itemUID, memberID=thisMember.memberID, reportID=thisMember.reportID, 
							otherXML=thisMember.otherXML, siteID=thisMember.siteID, strQryData=grabForProcessingStr);

						if (reportParamsStr.success) {
							memberPhotoUrl = memberPhotoUrl & lcase(reportParamsStr.membernumber) & ".jpg";
							arrayAppend(reportParams,{ name="xmlFilePath", value=reportParamsStr.reportXMLFile, datatype="filepath", needsEvaluation=false });
							arrayAppend(reportParams,{ name="reportTitle", value=toString(thisMember.reportName), datatype="string", needsEvaluation=false });
							arrayAppend(reportParams,{ name="generateDateStr", value=toString("Report generated#chr(10)##dateformat(now(),"m/d/yyyy")# #timeformat(now(),"h:mm tt")# Central"), datatype="string", needsEvaluation=false });
							arrayAppend(reportParams,{ name="givingHistoryTitle", value=toString(reportParamsStr.sectionnamegivinghistory), datatype="string", needsEvaluation=false });	
							arrayAppend(reportParams,{ name="paymentSummaryTitle", value=toString(reportParamsStr.sectionnamepaymentsummary), datatype="string", needsEvaluation=false });
							arrayAppend(reportParams,{ name="eventHistoryTitle", value=toString(reportParamsStr.sectionnameevents), datatype="string", needsEvaluation=false });
							arrayAppend(reportParams,{ name="subsHistoryTitle", value=toString(reportParamsStr.sectionnamesubscriptions), datatype="string", needsEvaluation=false });
							arrayAppend(reportParams,{ name="memberPhotoUrl", value=toString(memberPhotoUrl), datatype="string", needsEvaluation=false });
							arrayAppend(reportParams,{ name="showMemberPhotos", value=toString(reportParamsStr.frmshowphotos), datatype="string", needsEvaluation=false });

							// Create report
							reportCreated = birtRunner.runReport(reportPath=reportdesign, renderFormat="pdf", destinationPath=outputFileName, reportParams=reportParams);
							if (reportCreated) 
								queueSetStatus(queueType='callSheets', queueStatus='readyToNotify', itemUID=thisMember.itemUID, memberID=thisMember.memberID);
						}
						
					} catch (e) {
						application.objError.sendError(cfcatch=e, objectToDump=local);
						rethrow;
					}	
				}

			}, true, arguments.threads);
			</cfscript>

			<!--- ----------------------------------------------------------------------------------- --->
			<!--- post processing: process any notifications for itemUID-memberIDs that are readyToNotify --->
			<!--- ----------------------------------------------------------------------------------- --->
			<cftry>
				<cfquery name="local.qryNotifications" datasource="#application.dsn.platformQueue.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						EXEC dbo.job_CallSheets_grabForNotification;
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

				<cfif local.qryNotifications.recordcount>
					<cfquery name="local.qryItems" dbtype="query">
						select distinct itemUID, reportID, reportEmail, siteName, siteCode, firstname, lastname, membernumber, memberID
						from [local].qryNotifications
					</cfquery>

					<cftry>
						<cfloop query="local.qryItems">
							<!--- compile pdfs into one zip ---> 
							<cfif directoryExists("#application.paths.SharedTemp.path#callsheet_#local.qryItems.itemUID#")>
								<cfpdf action="merge" directory="#application.paths.SharedTemp.path#callsheet_#local.qryItems.itemUID#" 
									stoponerror="false" overwrite="yes" 
									destination="#application.paths.SharedTemp.path#callsheet_#local.qryItems.itemUID#/callSheet.pdf">							
							<cfelse>
								<cfthrow message="Folder of PDFs was not found">
							</cfif>						

							<!--- record site document --->
							<cfset local.documentResult = objReportHelper.generateReportDocument(reportID=local.qryItems.reportID, reportTitle="Call Sheet Report",
								siteID=application.objSiteInfo.mc_siteInfo[local.qryItems.siteCode].siteID, orgCode=application.objSiteInfo.mc_siteInfo[local.qryItems.siteCode].orgCode, 
								siteCode=local.qryItems.siteCode, directoryPath="#application.paths.SharedTemp.path#callsheet_#local.qryItems.itemUID#/callSheet.pdf", 
								reportFileName="callSheet.pdf", memberID=local.qryItems.memberID)>
							<cfif not local.documentResult.success>
								<cfthrow message="#local.documentResult.errorMessage#">
							</cfif>					
							
							<!--- prep and send email --->
							<cfset local.thisReportEmail = local.qryItems.reportEmail>
							<cfset local.thisSiteName = local.qryItems.siteName>
							<cfset local.thisSiteCode = local.qryItems.siteCode>

							<cfif len(local.thisReportEmail)>
								<cfsavecontent variable="local.thisEmailContent">
									<cfoutput>
									<div>
										We have completed generating your requested Member Call Sheets.<br/><br/>
									</div>
									#local.documentResult.downloadHTML#
									</cfoutput>
								</cfsavecontent>

								<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
									emailfrom={ name="MemberCentral", email=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].networkEmailFrom},
									emailto=[{ name="#local.qryItems.firstname# #local.qryItems.lastname#", email=local.thisReportEmail }],
									emailreplyto=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].supportProviderEmail,
									emailsubject="Call Sheets for #local.thisSiteName#",
									emailtitle="#local.thisSiteName# Call Sheets Report",
									emailhtmlcontent=local.thisEmailContent,
									siteID=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].siteID,
									memberID=val(local.qryItems.memberID),
									messageTypeID=arguments.messageTypeID,
									sendingSiteResourceID=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].siteSiteResourceID
									)>
							</cfif>

							<!--- update status --->
							<cfquery name="local.updateStatusInMass" datasource="#application.dsn.platformQueue.dsn#">
								SET NOCOUNT ON;

								DECLARE @doneStatusID INT;
								EXEC dbo.queue_getStatusIDbyType @queueType='callSheets', @queueStatus='done', @queueStatusID=@doneStatusID OUTPUT;

								update dbo.queue_callSheetsDetail WITH (UPDLOCK, HOLDLOCK)
								set statusID = @doneStatusID,
									dateUpdated = getdate()
								where itemUID = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#local.qryItems.itemUID#">;
							</cfquery>
						</cfloop>

					<cfcatch type="Any">
						<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
					</cfcatch>
					</cftry>
					
				</cfif>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>

			<!--- -------------------------------------------------------- --->
			<!--- post processing - delete any itemUIDs that are done --->
			<!--- -------------------------------------------------------- --->
			<cftry>
				<cfquery name="local.qryClear" datasource="#application.dsn.platformQueue.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						EXEC dbo.job_callSheets_clearDone;
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
				<cfcatch type="Any">
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				</cfcatch>
			</cftry>	

			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.returnStruct.success = false>
			</cfcatch>
		</cftry>
					
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="queueSetStatus" access="private" returntype="void" output="false">
		<cfargument name="queueType" type="string" required="true">
		<cfargument name="queueStatus" type="string" required="true">
		<cfargument name="itemUID" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfquery name="local.updateStatus" datasource="#application.dsn.platformQueue.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				
				declare @queueType varchar(25) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.queueType#">,
					@itemUID uniqueidentifier = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#arguments.itemUID#">,
					@queueStatus varchar(30) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.queueStatus#">,
					@memberID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">;

				declare @newstatus int;
				select @newstatus = qs.queueStatusID 
				from dbo.tblQueueStatuses as qs
				inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
				where qt.queueType = @queueType
				and qs.queueStatus = @queueStatus;

				IF @newstatus is not null
					update dbo.queue_callSheetsDetail WITH (UPDLOCK, HOLDLOCK)
					set statusID = @newstatus,
						dateUpdated = getdate()
					where itemUID = @itemUID
					and memberID = @memberID;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>	

	<cffunction name="queueItemHasStatus" access="private" returntype="boolean" output="false">
		<cfargument name="queueType" type="string" required="true">
		<cfargument name="queueStatus" type="string" required="true">
		<cfargument name="itemUID" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structnew()>

		<cfquery name="local.checkItemUID" datasource="#application.dsn.platformQueue.dsn#">
			select count(cd.itemUID) as itemCount
			from dbo.queue_callSheetsDetail as cd
			INNER JOIN dbo.tblQueueStatuses AS qs ON qs.queueStatusID = cd.statusID 
			INNER JOIN dbo.tblQueueTypes AS qt ON qt.queueTypeID = qs.queueTypeID
			where cd.itemUID = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#arguments.itemUID#">
			AND qt.queueType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.queueType#">
			AND qs.queueStatus = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.queueStatus#">
			AND cd.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
		</cfquery>

		<cfreturn (local.checkItemUID.itemCount gt 0)>
	</cffunction>	

	<cffunction name="queueGrabForProcessing" access="private" returntype="struct" output="false">
		<cfargument name="batchsize" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.returnStr = structnew()>

		<cfquery name="local.returnStr.qryGrabItems" datasource="#application.dsn.platformQueue.dsn#" result="local.qryGrabItemsResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				declare @batchSize int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.batchsize#">;
				EXEC dbo.job_CallSheets_grabForProcessing @batchsize=@batchsize;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.returnStr.qryFieldGroupSetData = queryNew("itemUID, memberid, membernumber, mc_combinedName, company, mc_topGroupsetTitle, mc_topGroupsetData, mc_topFieldsetDataPhone, mc_topFieldsetData, mc_bottomFieldsetData, mc_bottomGroupsetTitle, mc_bottomGroupsetData", "VarChar, Integer, VarChar, VarChar, VarChar, VarChar, VarChar, VarChar, VarChar, VarChar, VarChar, VarChar")>
		<cfset local.returnStr.qrySubs = queryNew("itemUID, memberid, subscription, SubscriptionStatus, subStartDate, subEndDate, billing_total, billing_BilledPrice, invoices_dueInRange, paid_amount", "VarChar, Integer, VarChar, VarChar, Date, Double, Double, Double, Double, Double")>	
		<cfset local.returnStr.qryEvents = queryNew("itemUID, reportMemberID, startTime, eventTitle, attended, creditsAwarded, reportOrder", "VarChar, Integer, Date, VarChar, Integer, Double,Integer")>	
		<cfset local.returnStr.qryGiving = queryNew("itemUID, reportMemberID, accountName, billedR, billedR1, billedR2, paidR, paidR1, paidR2, reportOrder, startR, endR, startR1, endR1, startR2, endR2", "VarChar, Integer, VarChar, Double, Double, Double, Double, Double, Double, Integer, Date, Date, Date, Date, Date, Date")>
		<cfset local.returnStr.qryHistory = queryNew("itemUID, reportMemberID, sectionOrder, sectionTitle, includeQtyAmt, userDate, userEndDate, quantity, dollarAmt, historyCategory, MemberName, memberCompany, linkedMember, linkedMemberCompany, historyContent,memberContent,reportOrder", "VarChar, Integer, Integer, VarChar, Double, Date, Date, Integer, Double, Integer, VarChar, VarChar, VarChar, VarChar, VarChar, VarChar, Integer")>	
		<cfset local.returnStr.qryPayments = queryNew("itemUID, reportMemberID, accountName, Paid, reportOrder", "VarChar, Integer, VarChar, Double, Integer")>
		<cfset local.returnStr.qryNotes = queryNew("itemUID, reportMemberID, sectionOrder, sectionTitle, userDate, historyCategory, MemberName, memberCompany, linkedMember, linkedMemberCompany, historyContent,memberContent,reportOrder", "VarChar, Integer, Integer, VarChar, Date, Integer, VarChar, VarChar, VarChar, VarChar, VarChar, VarChar, Integer")>
		<cfset local.returnStr.qryRelationships = queryNew("itemUID, reportMemberID, sectionOrder, sectionTitle, userDate, historyCategory, MemberName, memberCompany, linkedMember, linkedMemberCompany, historyContent,memberContent,reportOrder", "VarChar, Integer, Integer, VarChar, Date, Integer, VarChar, VarChar, VarChar, VarChar, VarChar, VarChar, Integer")>		

		<cfif local.returnStr.qryGrabItems.recordCount>
			<cfquery name="local.qryItems" dbtype="query">
				select distinct itemUID from [local].returnStr.qryGrabItems
			</cfquery>		

			<cfloop query="local.qryItems">
				
				<cfquery name="local.qryItemReportData" dbtype="query">
					select itemUID, memberID, reportID, reportName, otherXML, siteID
					from [local].returnStr.qryGrabItems
					where itemUID = <cfqueryparam value="#local.qryItems.itemUID#" cfsqltype="cf_sql_idstamp">
				</cfquery>				
			
				<cfset local.tempTableName = "rpt#getTickCount()#">
				<cfset local.reportInfoXML = XMLParse(local.qryItemReportData.otherXML)>

				<cfsetting requesttimeout="600">

				<!--- ---------------- --->
				<!--- fieldset at top  --->
				<!--- ---------------- --->
				<cfset local.arrFS = arrayNew(1)>
				<cfset local.fieldSetAtTop = XMLSearch(local.reportInfoXML,"string(/report/extra/fieldsetattop/text())")>
				<cfif len(local.fieldSetAtTop)>
					<cfset arrayAppend(local.arrFS,local.fieldSetAtTop)>
				</cfif>
				<cfset local.prepFieldsetsTop = prepSQL_fieldset(siteID=local.qryItemReportData.siteID, existingFields="m_lastname,m_firstname,m_membernumber,m_company",
																 existingAliases="m", fieldSetArray=local.arrFS, fieldSetNameFormat='', splitMultiValueDelim=', ',
																 combineAddressDelim=', ')>
				<cfset local.joinSQLOnlyMemberdataTop = arrayToList(local.prepFieldsetsTop.arrJoinsOnlyMemberData,' ')>
				<cfset local.selectSQLTop = arrayToList(local.prepFieldsetsTop.arrSelect)> 
				<cfset local.qryOutputFieldsTop = local.prepFieldsetsTop.qryOutputFields>

				<cfquery name="local.qryOutputFieldsForLoopTop" dbtype="query">
					select *
					from [local].qryOutputFieldsTop
					where fieldcodeSect NOT IN ('mc','m','ma','mp')
					or fieldCode IN ('m_recordtypeid','m_membertypeid','m_status')
				</cfquery>

				<!--- -------------------- --->
				<!--- fieldsets at bottom  --->
				<!--- -------------------- --->
				<cfset local.arrFS = arrayNew(1)>
				<cfloop array="#xmlsearch(local.reportInfoXML,"/report/fieldsets/fieldset")#" index="local.thisFS">
					<cfset arrayAppend(local.arrFS,local.thisFS.xmlAttributes.uid)>
				</cfloop>
				<cfset local.prepFieldsetsBottom = prepSQL_fieldset(siteID=local.qryItemReportData.siteID, existingFields="", existingAliases="m", fieldSetArray=local.arrFS, 
																	fieldSetNameFormat='', splitMultiValueDelim=', ', combineAddressDelim=', ')>
				<cfset local.joinSQLOnlyMemberdataBottom = arrayToList(local.prepFieldsetsBottom.arrJoinsOnlyMemberData,' ')>
				<cfset local.selectSQLBottom = arrayToList(local.prepFieldsetsBottom.arrSelect)> 
				<cfset local.qryOutputFieldsBottom = local.prepFieldsetsBottom.qryOutputFields>
				<cfquery name="local.qryOutputFieldsForLoopBottom" dbtype="query">
					select *
					from [local].qryOutputFieldsBottom
					where fieldcodeSect NOT IN ('mc','m','ma')
					or fieldCode IN ('m_recordtypeid','m_membertypeid','m_status')
				</cfquery>	

				<!--- -------------------- --->
				<!--- GROUPSETS at bottom  --->
				<!--- -------------------- --->
				<cfset local.arrGS = arrayNew(1)>
				<cfset local.listGS = "">
				<cfloop array="#xmlsearch(local.reportInfoXML,"/report/groupsets/groupset")#" index="local.thisGS">
					<cfset arrayAppend(local.arrGS,local.thisGS.xmlAttributes.uid)>
				</cfloop>
				<cfset local.listGS = arrayToList(local.arrGS)>
				
				<cfset local.groupSetAtTop = XMLSearch(local.reportInfoXML,"string(/report/extra/groupsetattop/text())")>

				<!--- show membernumber/company --->
				<cfset local.frmShowMemberNumber = XMLSearch(local.reportInfoXML,"string(/report/fieldsets/@mn)")>
				<cfset local.frmShowCompany = XMLSearch(local.reportInfoXML,"string(/report/fieldsets/@mc)")>

				<!--- main member query --->
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryTempFieldGroupSetData" result="local.qryDataResult">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						declare @orgID int = dbo.fn_getOrgIDFromSiteID(<cfqueryparam value="#local.qryItemReportData.siteID#" cfsqltype="cf_sql_integer">);

						IF OBJECT_ID('tempdb..##callSheetMembersTbl') IS NOT NULL
							DROP TABLE ##callSheetMembersTbl;	
						IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
							DROP TABLE ###local.tempTableName#;
						IF OBJECT_ID('tempdb..###local.tempTableName#MDMemberIDs') IS NOT NULL
							DROP TABLE ###local.tempTableName#MDMemberIDs;
						IF OBJECT_ID('tempdb..###local.tempTableName#MDFSTopResults') IS NOT NULL
							DROP TABLE ###local.tempTableName#MDFSTopResults;
						IF OBJECT_ID('tempdb..###local.tempTableName#MDFSBottomResults') IS NOT NULL
							DROP TABLE ###local.tempTableName#MDFSBottomResults;
						create table ##callSheetMembersTbl (memberID int);
						CREATE TABLE ###local.tempTableName#MDMemberIDs (memberID int PRIMARY KEY);
						CREATE TABLE ###local.tempTableName#MDFSTopResults (MCAutoID int IDENTITY(1,1) NOT NULL);
						CREATE TABLE ###local.tempTableName#MDFSBottomResults (MCAutoID int IDENTITY(1,1) NOT NULL);

						<cfloop query="local.qryItemReportData">
							insert into ##callSheetMembersTbl (memberID) values(#local.qryItemReportData.memberID#);
						</cfloop>
						
						select m.memberid, m.lastname, m.firstname, m.membernumber, m.company, 
							cast('' as varchar(max)) as mc_combinedName, cast('' as varchar(max)) as mc_combinedNameNoMemNum, cast('' as varchar(max)) as mc_topGroupsetTitle, 
							cast('' as varchar(max)) as mc_topGroupsetData, cast('' as varchar(max)) as mc_topFieldsetData, cast('' as varchar(max)) as mc_topFieldsetDataPhone, 
							cast('' as varchar(max)) as mc_bottomFieldsetData, cast('' as varchar(max)) as mc_bottomGroupsetTitle, cast('' as varchar(max)) as mc_bottomGroupsetData
							into ###local.tempTableName#
						from dbo.ams_members m	
						inner join ##callSheetMembersTbl as mtbl on mtbl.memberid = m.memberid
							and m.orgID = @orgID
							and m.memberID = m.activeMemberID
							and m.status <> 'D';
									
						CREATE NONCLUSTERED INDEX IX_memberid ON ###local.tempTableName# (memberID);

						<!--- ----------------------------------------------------------------------------------------------------- --->
						<!--- fieldset at top (even if no fieldset selected, there will be at least 1 field here - mc_combinedName) --->
						<!--- ----------------------------------------------------------------------------------------------------- --->

						<cfif len(local.prepFieldsetsTop.onlyMemberDataFieldNamesList) or len(local.prepFieldsetsBottom.onlyMemberDataFieldNamesList)>
							INSERT INTO ###local.tempTableName#MDMemberIDs (memberID)
							SELECT DISTINCT memberID
							FROM ###local.tempTableName#;
						</cfif>

						<cfif len(local.prepFieldsetsTop.onlyMemberDataFieldNamesList)>
							<cfset local.joinSQLOnlyMemberdataTop = replace(local.joinSQLOnlyMemberdataTop,'[[mockVWTableName]]','###local.tempTableName#MDFSTopResults')>

							EXEC membercentral.dbo.ams_getMemberDataByFields @orgID=@orgID, @fieldsList='#local.prepFieldsetsTop.onlyMemberDataFieldNamesList#',
								@membersTableName='###local.tempTableName#MDMemberIDs', @membersResultTableName='###local.tempTableName#MDFSTopResults';
						</cfif>

						IF OBJECT_ID('tempdb..###local.tempTableName#_2') IS NOT NULL
							DROP TABLE ###local.tempTableName#_2;
						select tm.memberid, tm.mc_topFieldsetDataPhone, tm.mc_topFieldsetData<cfif len(local.selectSQLTop)>, #PreserveSingleQuotes(local.selectSQLTop)#</cfif>
						into ###local.tempTableName#_2
						from ###local.tempTableName# as tm
						inner join ams_members as m on m.orgID = @orgID and tm.memberID = m.memberID
						<cfif len(local.joinSQLOnlyMemberdataTop)>#PreserveSingleQuotes(local.joinSQLOnlyMemberdataTop)#</cfif>;

						<!--- this is to update content objects with HTML that birt cannot render correctly -- like &nbsp; --->
						<cfloop query="local.qryOutputFieldsForLoopTop">
							<cfif local.qryOutputFieldsForLoopTop.dataTypeCode eq "CONTENTOBJ">
								UPDATE ###local.tempTableName#_2
								SET [#preserveSingleQuotes(local.qryOutputFieldsForLoopTop.fieldLabel)#] = 
									ltrim(rtrim(replace(replace([#preserveSingleQuotes(local.qryOutputFieldsForLoopTop.fieldLabel)#],'&nbsp;',' '),'&quot;','"')));
							</cfif>
						</cfloop>

						<cfloop query="local.qryOutputFieldsTop">
							<cfif local.qryOutputFieldsTop.fieldcodeSect eq "mp">
								update ###local.tempTableName#_2
								set mc_topFieldsetDataPhone = mc_topFieldsetDataPhone + case when len([#preserveSingleQuotes(local.qryOutputFieldsTop.fieldLabel)#]) > 0 then '<b>#local.qryOutputFieldsTop.fieldLabel#</b>: ' + [#preserveSingleQuotes(local.qryOutputFieldsTop.fieldLabel)#] + '<br>' else '' end;
							</cfif>
						</cfloop>			

						<cfloop query="local.qryOutputFieldsTop">
							<cfif local.qryOutputFieldsTop.dbObjectAlias eq "mc" and left(local.qryOutputFieldsTop.fieldCode,18) eq "mc_combinedAddress">
								update ###local.tempTableName#_2
								set mc_topFieldsetData = mc_topFieldsetData + case when len(replace([#local.qryOutputFieldsTop.dbfield#],', , ',', ')) > 1 then '<b>#local.qryOutputFieldsTop.fieldLabel#</b>: ' + replace([#local.qryOutputFieldsTop.dbfield#],', , ',', ') + '<br>' else '' end;
							</cfif>
						</cfloop>			
										
						<cfif local.qryOutputFieldsForLoopTop.recordcount>
							update ###local.tempTableName#_2
							set mc_topFieldsetData = mc_topFieldsetData
							<cfloop query="local.qryOutputFieldsForLoopTop">
								+ case when len(cast([#preserveSingleQuotes(local.qryOutputFieldsForLoopTop.fieldLabel)#] as varchar(max))) > 0 then '<b>#local.qryOutputFieldsForLoopTop.fieldLabel#</b>: ' + cast([#preserveSingleQuotes(local.qryOutputFieldsForLoopTop.fieldLabel)#] as varchar(max)) + '<br>' else '' end
							</cfloop>;
						</cfif>
						
						update tm
						set tm.mc_combinedName = tm2.mc_combinedName, 
							tm.mc_combinedNameNoMemNum = tm2.mc_combinedNameNoMemNum,
							tm.mc_topFieldsetDataPhone = tm2.mc_topFieldsetDataPhone, 
							tm.mc_topFieldsetData = tm2.mc_topFieldsetData
						from ###local.tempTableName# as tm
						inner join ###local.tempTableName#_2 as tm2 on tm.memberID = tm2.memberID;

						IF OBJECT_ID('tempdb..###local.tempTableName#_2') IS NOT NULL
							DROP TABLE ###local.tempTableName#_2;

						<!--- ------------------- --->
						<!--- fieldsets at bottom --->
						<!--- ------------------- --->
						<cfif local.qryOutputFieldsBottom.recordCount>

							<cfif len(local.prepFieldsetsBottom.onlyMemberDataFieldNamesList)>
								<cfset local.joinSQLOnlyMemberdataBottom = replace(local.joinSQLOnlyMemberdataBottom,'[[mockVWTableName]]','###local.tempTableName#MDFSBottomResults')>
								
								EXEC membercentral.dbo.ams_getMemberDataByFields @orgID=@orgID, @fieldsList='#local.prepFieldsetsBottom.onlyMemberDataFieldNamesList#',
									@membersTableName='###local.tempTableName#MDMemberIDs', @membersResultTableName='###local.tempTableName#MDFSBottomResults';
							</cfif>
			
							IF OBJECT_ID('tempdb..###local.tempTableName#_3') IS NOT NULL
								DROP TABLE ###local.tempTableName#_3;
							select tm.memberid, tm.mc_bottomFieldsetData<cfif len(local.selectSQLBottom)>, #PreserveSingleQuotes(local.selectSQLBottom)#</cfif>
							into ###local.tempTableName#_3
							from ###local.tempTableName# as tm
							inner join ams_members as m on m.orgID = @orgID and tm.memberID = m.memberID
							<cfif len(local.joinSQLOnlyMemberdataBottom)>#PreserveSingleQuotes(local.joinSQLOnlyMemberdataBottom)#</cfif>;

							<!--- this is to update content objects with HTML that birt cannot render correctly -- like &nbsp; --->
							<cfloop query="local.qryOutputFieldsForLoopBottom">
								<cfif local.qryOutputFieldsForLoopBottom.dataTypeCode eq "CONTENTOBJ">
									UPDATE ###local.tempTableName#_3
									SET [#preserveSingleQuotes(local.qryOutputFieldsForLoopBottom.fieldLabel)#] = 
										ltrim(rtrim(replace(replace([#preserveSingleQuotes(local.qryOutputFieldsForLoopBottom.fieldLabel)#],'&nbsp;',' '),'&quot;','"')));
								</cfif>
							</cfloop>

							<cfloop query="local.qryOutputFieldsBottom">
								<cfif local.qryOutputFieldsBottom.dbObjectAlias eq "mc" and left(local.qryOutputFieldsBottom.fieldCode,18) eq "mc_combinedAddress">
									update ###local.tempTableName#_3
									set mc_bottomFieldsetData = mc_bottomFieldsetData + case when len(replace([#local.qryOutputFieldsBottom.dbfield#],', , ',', ')) > 1 then '<b>#local.qryOutputFieldsBottom.fieldLabel#</b>: ' + replace([#local.qryOutputFieldsBottom.dbfield#],', , ',', ') + '<br>' else '' end;
								</cfif>
							</cfloop>			

							<cfif local.qryOutputFieldsForLoopBottom.recordcount>
								update ###local.tempTableName#_3
								set mc_bottomFieldsetData = mc_bottomFieldsetData
								<cfloop query="local.qryOutputFieldsForLoopBottom">
									<cfif NOT listFindNoCase("mc,m,ma",local.qryOutputFieldsForLoopBottom.fieldcodeSect)>
										+ case when len(cast([#preserveSingleQuotes(local.qryOutputFieldsForLoopBottom.fieldLabel)#] as varchar(max))) > 0 then '<b>#local.qryOutputFieldsForLoopBottom.fieldLabel#</b>: ' + cast([#preserveSingleQuotes(local.qryOutputFieldsForLoopBottom.fieldLabel)#] as varchar(max)) + '<br>' else '' end
									</cfif>
								</cfloop>;
							</cfif>

							update tm
							set tm.mc_bottomFieldsetData = tm3.mc_bottomFieldsetData
							from ###local.tempTableName# as tm
							inner join ###local.tempTableName#_3 as tm3 on tm.memberID = tm3.memberID;
			
							IF OBJECT_ID('tempdb..###local.tempTableName#_3') IS NOT NULL
								DROP TABLE ###local.tempTableName#_3;
							IF OBJECT_ID('tempdb..##callSheetMembersTbl') IS NOT NULL
								DROP TABLE ##callSheetMembersTbl;
						</cfif>
						
						<!--- --------------- --->
						<!--- groupset at top --->
						<!--- --------------- --->
						<cfif len(local.groupSetAtTop)>
							IF OBJECT_ID('tempdb..###local.tempTableName#_4') IS NOT NULL
								DROP TABLE ###local.tempTableName#_4;

							with allGSData AS (
								select m.memberID, mgs.groupSetName, isnull(nullif(mgsg.labelOverride,''),g.groupName) as groupName
								from ###local.tempTableName# as m
								inner join dbo.cache_members_groups as mg on mg.orgID = @orgID and mg.memberid = m.memberid
								inner join dbo.ams_groups as g on g.orgID = @orgID and g.groupID = mg.groupID and g.status <> 'D'
								inner join dbo.ams_memberGroupSetGroups as mgsg on mgsg.groupID = g.groupID
								inner join dbo.ams_memberGroupSets mgs on mgs.orgID = @orgID and mgs.groupSetID = mgsg.groupSetID
								where mgs.groupSetID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.groupSetAtTop#">
							)
							select memberID, groupSetName as mc_topGroupsetTitle, 
								replace(replace(STUFF((SELECT '|' + replace(t2.groupName,'|',char(7))
										FROM allGSData as t2
										WHERE t1.memberID=t2.memberID
										ORDER BY t2.groupName
										FOR XML PATH(''), TYPE).value('.','varchar(max)'),1,1,''),'|','<br>'),char(7),'|') as mc_topGroupsetData
							into ###local.tempTableName#_4
							from allGSData as t1;

							update tm
							set tm.mc_topGroupsetTitle = tm4.mc_topGroupsetTitle, 
								tm.mc_topGroupsetData = tm4.mc_topGroupsetData + '<br>'
							from ###local.tempTableName# as tm
							inner join ###local.tempTableName#_4 as tm4 on tm.memberID = tm4.memberID;
			
							IF OBJECT_ID('tempdb..###local.tempTableName#_4') IS NOT NULL
								DROP TABLE ###local.tempTableName#_4;
						</cfif>
						
						<!--- --------------- --->
						<!--- groupset at bottom --->
						<!--- --------------- --->
						<cfif listLen(local.listGS)>
							IF OBJECT_ID('tempdb..###local.tempTableName#_5') IS NOT NULL
								DROP TABLE ###local.tempTableName#_5;

							with bottomGSData AS (
								select m.memberID, mgs.groupSetName, isnull(nullif(mgsg.labelOverride,''),g.groupName) as groupName
								from ###local.tempTableName# as m
								inner join dbo.cache_members_groups as mg on mg.orgID = @orgID and mg.memberid = m.memberid
								inner join dbo.ams_groups as g on g.orgID = @orgID and g.groupID = mg.groupID and g.status <> 'D'
								inner join dbo.ams_memberGroupSetGroups as mgsg on mgsg.groupID = g.groupID
								inner join dbo.ams_memberGroupSets mgs on mgs.orgID = @orgID and mgs.groupSetID = mgsg.groupSetID
								where mgs.uid in (<cfqueryparam value="#local.listGS#" cfsqltype="cf_sql_varchar" list="true">)
							)
							select memberID, groupSetName as mc_bottomGroupsetTitle, 
								replace(replace(STUFF((SELECT '|' + replace(t2.groupName,'|',char(7))
										FROM bottomGSData as t2
										WHERE t1.memberID=t2.memberID and t1.groupName = t2.groupName
										ORDER BY t2.groupName
										FOR XML PATH(''), TYPE).value('.','varchar(max)'),1,1,''),'|','<br>'),char(7),'|') as mc_bottomGroupsetData,
										ROW_NUMBER() OVER (ORDER BY memberID, groupSetName) as row
							into ###local.tempTableName#_5
							from bottomGSData as t1;

							declare @loopRowID int, @loopMemberID int, @loopMc_bottomGroupsetData varchar(1000), @loopMc_bottomGroupsetTitle varchar(1000);
							select @loopRowID =  min(row) from ###local.tempTableName#_5;
							while @loopRowID is not null begin
								select 
									@loopMemberID = memberid, 
									@loopMc_bottomGroupsetTitle = mc_bottomGroupsetTitle, 
									@loopMc_bottomGroupsetData =  mc_bottomGroupsetData
								from ###local.tempTableName#_5
								where row = @loopRowID;

								if len(@loopMc_bottomGroupsetTitle) > 0
									update tm
									set tm.mc_bottomGroupsetTitle = tm.mc_bottomGroupsetTitle + case when len(tm.mc_bottomGroupsetTitle) > 0 then ' ' else '' end +  @loopMc_bottomGroupsetTitle, 
										tm.mc_bottomGroupsetData = tm.mc_bottomGroupsetData + 
										case when len(tm.mc_bottomGroupsetData) > 0 then '<br>' else '' end + 
										case when len(@loopMc_bottomGroupsetData) > 0 then '<b>' + @loopMc_bottomGroupsetTitle + '</b>: ' else '' end + @loopMc_bottomGroupsetData
									from ###local.tempTableName# as tm
									where tm.memberID = @loopMemberID;

								select @loopRowID =  min(row) from ###local.tempTableName#_5 where row > @loopRowID;
							end
			
							IF OBJECT_ID('tempdb..###local.tempTableName#_5') IS NOT NULL
								DROP TABLE ###local.tempTableName#_5;
						</cfif>				

						select tm.memberid, tm.membernumber, tm.mc_combinedName, tm.mc_combinedNameNoMemNum, tm.company, tm.mc_topGroupsetTitle, tm.mc_topGroupsetData, 
							tm.mc_topFieldsetDataPhone, tm.mc_topFieldsetData, tm.mc_bottomFieldsetData, tm.mc_bottomGroupsetTitle, tm.mc_bottomGroupsetData
						from ###local.tempTableName# as tm
						order by tm.mc_combinedName;

						IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
							DROP TABLE ###local.tempTableName#;
						IF OBJECT_ID('tempdb..###local.tempTableName#MDMemberIDs') IS NOT NULL
							DROP TABLE ###local.tempTableName#MDMemberIDs;
						IF OBJECT_ID('tempdb..###local.tempTableName#MDFSTopResults') IS NOT NULL
							DROP TABLE ###local.tempTableName#MDFSTopResults;
						IF OBJECT_ID('tempdb..###local.tempTableName#MDFSBottomResults') IS NOT NULL
							DROP TABLE ###local.tempTableName#MDFSBottomResults;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

				<cfloop query="local.qryTempFieldGroupSetData">
      				<cfset local.tempRowStr = StructNew()>
      				<cfset local.tempRowStr.itemUID = local.qryItems.itemUID>	
					<cfset local.tempRowStr.memberid = local.qryTempFieldGroupSetData.memberID>
					<cfset local.tempRowStr.membernumber = local.qryTempFieldGroupSetData.membernumber>
					<cfif local.frmShowMemberNumber is 1>
						<cfset local.tempRowStr.mc_combinedName = local.qryTempFieldGroupSetData.mc_combinedName>
					<cfelse>
						<cfset local.tempRowStr.mc_combinedName = local.qryTempFieldGroupSetData.mc_combinedNameNoMemNum>
					</cfif>
					<cfif local.frmShowCompany is 1 AND len(local.qryTempFieldGroupSetData.company)>
						<cfset local.tempRowStr.company = local.qryTempFieldGroupSetData.company>
					<cfelse>
						<cfset local.tempRowStr.company = ''>
					</cfif>
					<cfset local.tempRowStr.mc_topGroupsetTitle = local.qryTempFieldGroupSetData.mc_topGroupsetTitle>	
					<cfset local.tempRowStr.mc_topGroupsetData = local.qryTempFieldGroupSetData.mc_topGroupsetData>	
					<cfset local.tempRowStr.mc_topFieldsetDataPhone = local.qryTempFieldGroupSetData.mc_topFieldsetDataPhone>	
					<cfset local.tempRowStr.mc_topFieldsetData = local.qryTempFieldGroupSetData.mc_topFieldsetData>	
					<cfset local.tempRowStr.mc_bottomFieldsetData = local.qryTempFieldGroupSetData.mc_bottomFieldsetData>	
					<cfset local.tempRowStr.mc_bottomGroupsetTitle = local.qryTempFieldGroupSetData.mc_bottomGroupsetTitle>	
					<cfset local.tempRowStr.mc_bottomGroupsetData = local.qryTempFieldGroupSetData.mc_bottomGroupsetData>	
					<cfset queryAddRow(local.returnStr.qryFieldGroupSetData,local.tempRowStr)>								
				</cfloop>	

				<cfif arrayLen(XMLSearch(local.reportInfoXML,"/report/subrule//condition"))>					
					<cfset local.qryTempSubs = runSubReport_subs(itemUID=local.qryItems.itemUID, reportID=local.qryItemReportData.reportID, otherXML=local.qryItemReportData.otherXML)>
					<cfloop query="local.qryTempSubs">
	      				<cfset local.tempRowStr = StructNew()>
	      				<cfset local.tempRowStr.itemUID = local.qryTempSubs.itemUID>	
						<cfset local.tempRowStr.memberid = local.qryTempSubs.memberID>
						<cfset local.tempRowStr.subscription = local.qryTempSubs.subscription>	
						<cfset local.tempRowStr.SubscriptionStatus = local.qryTempSubs.SubscriptionStatus>
						<cfset local.tempRowStr.subStartDate = local.qryTempSubs.subStartDate>	
						<cfset local.tempRowStr.subEndDate = local.qryTempSubs.subEndDate>	
						<cfset local.tempRowStr.billing_total = local.qryTempSubs.billing_total>	
						<cfset local.tempRowStr.billing_BilledPrice = local.qryTempSubs.billing_BilledPrice>	
						<cfset local.tempRowStr.invoices_dueInRange = local.qryTempSubs.invoices_dueInRange>
						<cfset local.tempRowStr.paid_amount = local.qryTempSubs.paid_amount>
						<cfset queryAddRow(local.returnStr.qrySubs,local.tempRowStr)>								
					</cfloop>
				</cfif>

				<cfif len(XMLSearch(local.reportInfoXML,"string(/report/extra/sectionnameevents/text())"))>
					<cfset local.qryTempEvents = runSubReport_events(itemUID=local.qryItems.itemUID, reportID=local.qryItemReportData.reportID, otherXML=local.qryItemReportData.otherXML)>
					<cfloop query="local.qryTempEvents">
	      				<cfset local.tempRowStr = StructNew()>
	      				<cfset local.tempRowStr.itemUID = local.qryTempEvents.itemUID>	
						<cfset local.tempRowStr.reportMemberID = local.qryTempEvents.reportMemberID>
						<cfset local.tempRowStr.startTime = local.qryTempEvents.startTime>	
						<cfset local.tempRowStr.eventTitle = local.qryTempEvents.eventTitle>	
						<cfset local.tempRowStr.attended = local.qryTempEvents.attended>	
						<cfset local.tempRowStr.creditsAwarded = local.qryTempEvents.creditsAwarded>	
						<cfset local.tempRowStr.reportOrder = local.qryTempEvents.reportOrder>
						<cfset queryAddRow(local.returnStr.qryEvents,local.tempRowStr)>								
					</cfloop>
				</cfif>

				<cfif len(XMLSearch(local.reportInfoXML,"string(/report/extra/sectionnamegivinghistory/text())"))>
					<cfset local.qryTempGiving = runSubReport_giving(itemUID=local.qryItems.itemUID, reportID=local.qryItemReportData.reportID, otherXML=local.qryItemReportData.otherXML)>
					<cfloop query="local.qryTempGiving">
	      				<cfset local.tempRowStr = StructNew()>
	      				<cfset local.tempRowStr.itemUID = local.qryTempGiving.itemUID>	
						<cfset local.tempRowStr.reportMemberID = local.qryTempGiving.reportMemberID>
						<cfset local.tempRowStr.accountName = local.qryTempGiving.accountName>	
						<cfset local.tempRowStr.billedR = local.qryTempGiving.billedR>	
						<cfset local.tempRowStr.billedR1 = local.qryTempGiving.billedR1>	
						<cfset local.tempRowStr.billedR2 = local.qryTempGiving.billedR2>	
						<cfset local.tempRowStr.paidR = local.qryTempGiving.paidR>
						<cfset local.tempRowStr.paidR1 = local.qryTempGiving.paidR1>
						<cfset local.tempRowStr.paidR2 = local.qryTempGiving.paidR2>
						<cfset local.tempRowStr.reportOrder = local.qryTempGiving.reportOrder>
						<cfset local.tempRowStr.startR = local.qryTempGiving.startR>
						<cfset local.tempRowStr.endR = local.qryTempGiving.endR>
						<cfset local.tempRowStr.startR1 = local.qryTempGiving.startR1>
						<cfset local.tempRowStr.endR1 = local.qryTempGiving.endR1>
						<cfset local.tempRowStr.startR2 = local.qryTempGiving.startR2>
						<cfset local.tempRowStr.endR2 = local.qryTempGiving.endR2>
						<cfset queryAddRow(local.returnStr.qryGiving,local.tempRowStr)>								
					</cfloop>
				</cfif>

				<cfset local.qryTempHistory = runSubReport_history(itemUID=local.qryItems.itemUID, reportID=local.qryItemReportData.reportID, otherXML=local.qryItemReportData.otherXML)>
				<cfloop query="local.qryTempHistory">
      				<cfset local.tempRowStr = StructNew()>
      				<cfset local.tempRowStr.itemUID = local.qryTempHistory.itemUID>	
					<cfset local.tempRowStr.reportMemberID = local.qryTempHistory.reportMemberID>
					<cfset local.tempRowStr.sectionOrder = local.qryTempHistory.sectionOrder>	
					<cfset local.tempRowStr.sectionTitle = local.qryTempHistory.sectionTitle>	
					<cfset local.tempRowStr.includeQtyAmt = local.qryTempHistory.includeQtyAmt>	
					<cfset local.tempRowStr.userDate = local.qryTempHistory.userDate>	
					<cfset local.tempRowStr.userEndDate = local.qryTempHistory.userEndDate>
					<cfset local.tempRowStr.quantity = local.qryTempHistory.quantity>
					<cfset local.tempRowStr.dollarAmt = local.qryTempHistory.dollarAmt>
					<cfset local.tempRowStr.historyCategory = local.qryTempHistory.historyCategory>
					<cfset local.tempRowStr.MemberName = local.qryTempHistory.MemberName>
					<cfset local.tempRowStr.memberCompany = local.qryTempHistory.memberCompany>
					<cfset local.tempRowStr.linkedMember = local.qryTempHistory.linkedMember>
					<cfset local.tempRowStr.linkedMemberCompany = local.qryTempHistory.linkedMemberCompany>
					<cfset local.tempRowStr.historyContent = local.qryTempHistory.historyContent>
					<cfset local.tempRowStr.memberContent = local.qryTempHistory.memberContent>
					<cfset local.tempRowStr.reportOrder = local.qryTempHistory.reportOrder>
					<cfset queryAddRow(local.returnStr.qryHistory,local.tempRowStr)>								
				</cfloop>	

				<cfif len(XMLSearch(local.reportInfoXML,"string(/report/extra/sectionnamepaymentsummary/text())"))>
					<cfset local.qryTempPayments = runSubReport_payments(itemUID=local.qryItems.itemUID, reportID=local.qryItemReportData.reportID, otherXML=local.qryItemReportData.otherXML)>
					<cfloop query="local.qryTempPayments">
	      				<cfset local.tempRowStr = StructNew()>
	      				<cfset local.tempRowStr.itemUID = local.qryTempPayments.itemUID>	
						<cfset local.tempRowStr.reportMemberID = local.qryTempPayments.reportMemberID>
						<cfset local.tempRowStr.accountName = local.qryTempPayments.accountName>	
						<cfset local.tempRowStr.Paid = local.qryTempPayments.Paid>	
						<cfset local.tempRowStr.reportOrder = local.qryTempPayments.reportOrder>	
						<cfset queryAddRow(local.returnStr.qryPayments,local.tempRowStr)>								
					</cfloop>
				</cfif>

				<cfset local.qryTempNotes = runSubReport_notes(itemUID=local.qryItems.itemUID, reportID=local.qryItemReportData.reportID, otherXML=local.qryItemReportData.otherXML)>
				<cfloop query="local.qryTempNotes">
      				<cfset local.tempRowStr = StructNew()>
      				<cfset local.tempRowStr.itemUID = local.qryTempNotes.itemUID>	
					<cfset local.tempRowStr.reportMemberID = local.qryTempNotes.reportMemberID>
					<cfset local.tempRowStr.sectionOrder = local.qryTempNotes.sectionOrder>	
					<cfset local.tempRowStr.sectionTitle = local.qryTempNotes.sectionTitle>
					<cfset local.tempRowStr.userDate = local.qryTempNotes.userDate>	
					<cfset local.tempRowStr.historyCategory = local.qryTempNotes.historyCategory>
					<cfset local.tempRowStr.MemberName = local.qryTempNotes.MemberName>
					<cfset local.tempRowStr.memberCompany = local.qryTempNotes.memberCompany>
					<cfset local.tempRowStr.linkedMember = local.qryTempNotes.linkedMember>
					<cfset local.tempRowStr.linkedMemberCompany = local.qryTempNotes.linkedMemberCompany>
					<cfset local.tempRowStr.historyContent = local.qryTempNotes.historyContent>
					<cfset local.tempRowStr.memberContent = local.qryTempNotes.memberContent>
					<cfset local.tempRowStr.reportOrder = local.qryTempNotes.reportOrder>
					<cfset queryAddRow(local.returnStr.qryNotes,local.tempRowStr)>								
				</cfloop>	
															
				<cfset local.qryTempRelationships = runSubReport_relationships(itemUID=local.qryItems.itemUID, reportID=local.qryItemReportData.reportID, otherXML=local.qryItemReportData.otherXML)>
				<cfloop query="local.qryTempRelationships">
      				<cfset local.tempRowStr = StructNew()>
      				<cfset local.tempRowStr.itemUID = local.qryTempRelationships.itemUID>	
					<cfset local.tempRowStr.reportMemberID = local.qryTempRelationships.reportMemberID>
					<cfset local.tempRowStr.sectionOrder = local.qryTempRelationships.sectionOrder>	
					<cfset local.tempRowStr.sectionTitle = local.qryTempRelationships.sectionTitle>
					<cfset local.tempRowStr.userDate = local.qryTempRelationships.userDate>	
					<cfset local.tempRowStr.historyCategory = local.qryTempRelationships.historyCategory>
					<cfset local.tempRowStr.MemberName = local.qryTempRelationships.MemberName>
					<cfset local.tempRowStr.memberCompany = local.qryTempRelationships.memberCompany>
					<cfset local.tempRowStr.linkedMember = local.qryTempRelationships.linkedMember>
					<cfset local.tempRowStr.linkedMemberCompany = local.qryTempRelationships.linkedMemberCompany>
					<cfset local.tempRowStr.historyContent = local.qryTempRelationships.historyContent>
					<cfset local.tempRowStr.memberContent = local.qryTempRelationships.memberContent>
					<cfset local.tempRowStr.reportOrder = local.qryTempRelationships.reportOrder>
					<cfset queryAddRow(local.returnStr.qryRelationships,local.tempRowStr)>								
				</cfloop>									

			</cfloop>
		</cfif>		

		<cfreturn local.returnStr>
	</cffunction>		
	
	<cffunction name="getBirtParams" access="private" returntype="struct" output="false">
		<cfargument name="itemUID" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="reportID" type="numeric" required="true">
		<cfargument name="otherXML" type="string" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="strQryData" type="struct" required="true">

		<cfset var local = structnew()>
		<cfset local.returnStr = structnew()>
		<cfset local.returnStr.success = true>

		<cftry>	

			<cfset local.returnStr.sectionnamegivinghistory = XMLSearch(arguments.otherXML,"string(/report/extra/sectionnamegivinghistory/text())")>
			<cfset local.returnStr.sectionnamesubscriptions = XMLSearch(arguments.otherXML,"string(/report/extra/sectionnamesubscriptions/text())")>
			<cfset local.returnStr.sectionnameevents = XMLSearch(arguments.otherXML,"string(/report/extra/sectionnameevents/text())")>
			<cfset local.returnStr.sectionnamepaymentsummary = XMLSearch(arguments.otherXML,"string(/report/extra/sectionnamepaymentsummary/text())")>
			<cfset local.returnStr.sectionnamegroupsets = XMLSearch(arguments.otherXML,"string(/report/extra/sectionnamegroupsets/text())")>	
			<cfset local.returnStr.frmshowphotos = XMLSearch(arguments.otherXML,"string(/report/extra/frmshowphotos/text())")>

			<cfset local.returnStr.reportXMLFile = "#application.paths.SharedTemp.path#callsheet_#arguments.itemUID#/#arguments.memberID#.xml">	
			<!---  Get fielset and group set data --->
			<cfquery name="local.qryData" dbtype="query">
				select * from [arguments].strQryData.qryFieldGroupSetData where memberID = #arguments.memberID# and itemUID = '#arguments.itemUID#'
			</cfquery>

			<cfset local.returnStr.membernumber = local.qryData.membernumber>

			<!---  Get Subs data --->
			<cfquery name="local.qrySubsData" dbtype="query">
				select * from [arguments].strQryData.qrySubs where memberID = #arguments.memberID# and itemUID = '#arguments.itemUID#'
			</cfquery>	

			<cfif not local.qrySubsData.recordCount>
				<cfset local.returnStr.sectionnamesubscriptions = "">
			</cfif>					

			<!---  Get Events data --->
			<cfquery name="local.qryEventsData" dbtype="query">
				select * from [arguments].strQryData.qryEvents where reportMemberID = #arguments.memberID# and itemUID = '#arguments.itemUID#'
			</cfquery>

			<cfif not local.qryEventsData.recordCount>
				<cfset local.returnStr.sectionnameevents = "">
			</cfif>				

			<!---  Get Giving History data --->
			<cfquery name="local.qryGivingData" dbtype="query">
				select * from [arguments].strQryData.qryGiving where reportMemberID = #arguments.memberID# and itemUID = '#arguments.itemUID#'
			</cfquery>

			<cfif not local.qryGivingData.recordCount>
				<cfset local.returnStr.sectionnamegivinghistory = "">
			</cfif>			

			<!---  Get Member History data --->
			<cfquery name="local.qryHistoryData" dbtype="query">
				select * from [arguments].strQryData.qryHistory where reportMemberID = #arguments.memberID# and itemUID = '#arguments.itemUID#'
			</cfquery>			

			<cfquery name="local.qryHistorySections" dbtype="query">
				select distinct sectionTitle, sectionOrder from [local].qryHistoryData order by sectionOrder
			</cfquery>

			<!---  Get Payments data --->
			<cfquery name="local.qryPaymentData" dbtype="query">
				select * from [arguments].strQryData.qryPayments where reportMemberID = #arguments.memberID# and itemUID = '#arguments.itemUID#'
			</cfquery>
			
			<cfif not local.qryPaymentData.recordCount>
				<cfset local.returnStr.sectionnamepaymentsummary = "">
			</cfif>				

			<!---  Get Notes data --->
			<cfquery name="local.qryNoteData" dbtype="query">
				select * from [arguments].strQryData.qryNotes where reportMemberID = #arguments.memberID# and itemUID = '#arguments.itemUID#'
			</cfquery>			

			<cfquery name="local.qryNoteSections" dbtype="query">
				select distinct sectionTitle, sectionOrder from [local].qryNoteData order by sectionOrder
			</cfquery>	

			<!---  Get Notes data --->
			<cfquery name="local.qryRelationshipData" dbtype="query">
				select * from [arguments].strQryData.qryRelationships where reportMemberID = #arguments.memberID# and itemUID = '#arguments.itemUID#'
			</cfquery>			

			<cfquery name="local.qryRelationshipSections" dbtype="query">
				select distinct sectionTitle, sectionOrder from [local].qryRelationshipData order by sectionOrder
			</cfquery>				

			<cfsavecontent variable="local.birtReportXML">
				<cfoutput>
				<members>
					<member memberID="#arguments.memberID#" 
						mc_combinedName="#xmlFormat(local.qryData.mc_combinedName)#" 
						membernumber="#xmlFormat(local.qryData.membernumber)#" 
						company="#xmlFormat(local.qryData.company)#" 
						mc_topFieldsetDataPhone="#xmlFormat(local.qryData.mc_topFieldsetDataPhone)#" 
						mc_topFieldsetData="#xmlFormat(local.qryData.mc_topFieldsetData)#" 
						mc_topGroupsetTitle="#xmlFormat(local.qryData.mc_topGroupsetTitle)#" 
						mc_topGroupsetData="#xmlFormat(local.qryData.mc_topGroupsetData)#" 
						mc_bottomFieldsetData="#xmlFormat(local.qryData.mc_bottomFieldsetData)#" 
						mc_bottomGroupsetTitle="#xmlFormat(local.returnStr.sectionnamegroupsets)#"
						mc_bottomGroupsetData="#xmlFormat(local.qryData.mc_bottomGroupsetData)#">
						<cfif local.qrySubsData.recordCount>
							<subs memberID="#arguments.memberID#">
							<cfloop query="local.qrySubsData">
								<sub subscriptionid="0" 
										memberID="#arguments.memberID#" 
										subscription="#xmlFormat(local.qrySubsData.subscription)#" 
										subscriptionstatus="#xmlFormat(local.qrySubsData.subscriptionstatus)#" 
										substartdate="#xmlFormat(dateFormat(local.qrySubsData.substartdate, 'mm/dd/yyyy'))#" 
										subenddate="#xmlFormat(dateFormat(local.qrySubsData.subenddate, 'mm/dd/yyyy'))#" 
										totPayments="#local.qrySubsData.billing_BilledPrice#" 
										totBilling ="#local.qrySubsData.billing_total#" 
										totBalanceDue="#local.qrySubsData.invoices_dueInRange#"
										paidAmount="#local.qrySubsData.paid_amount#"/>
							</cfloop>
							</subs>
						<cfelse>
							<subs/>
						</cfif>
						<cfif local.qryEventsData.recordCount>
							<events memberID="#arguments.memberID#">
							<cfloop query="local.qryEventsData">
								<event memberID="#arguments.memberID#" 
									startTime="#xmlFormat(dateFormat(local.qryEventsData.startTime, 'mm/dd/yyyy'))#" 
									eventTitle="#xmlFormat(local.qryEventsData.eventTitle)#" 
									attended="#yesNoFormat(val(local.qryEventsData.attended))#" 
									creditsAwarded="#local.qryEventsData.creditsAwarded#" />
							</cfloop>
							</events>
						<cfelse>
							<events/>
						</cfif>
						<cfif local.qryGivingData.recordCount>
							<givinghistory memberID="#arguments.memberID#" 
								StartR="#xmlFormat(dateFormat(local.qryGivingData.startR, 'mm/dd/yyyy'))#" 
								EndR="#xmlFormat(dateFormat(local.qryGivingData.endR, 'mm/dd/yyyy'))#" 
								StartR1="#xmlFormat(dateFormat(local.qryGivingData.startR1, 'mm/dd/yyyy'))#" 
								EndR1="#xmlFormat(dateFormat(local.qryGivingData.endR1, 'mm/dd/yyyy'))#" 
								StartR2="#xmlFormat(dateFormat(local.qryGivingData.startR2, 'mm/dd/yyyy'))#" 
								EndR2="#xmlFormat(dateFormat(local.qryGivingData.endR2, 'mm/dd/yyyy'))#">
							<cfloop query="local.qryGivingData">
								<history memberID="#arguments.memberID#" 
										AccountName="#xmlFormat(local.qryGivingData.AccountName)#" 
										BilledR="#local.qryGivingData.BilledR#" 
										PaidR="#local.qryGivingData.PaidR#" 
										BilledR1="#local.qryGivingData.BilledR1#" 
										PaidR1="#local.qryGivingData.PaidR1#" 
										BilledR2="#local.qryGivingData.BilledR2#" 
										PaidR2="#local.qryGivingData.PaidR2#"/>
							</cfloop>
							</givinghistory>
						<cfelse>
							<givinghistory/>
						</cfif>
						<cfif local.qryHistoryData.recordCount>
							<memberhistory memberID="#arguments.memberID#">
							<cfloop query="local.qryHistorySections">
								<cfquery name="local.qryThisHistoryData" dbtype="query">
									select * from [local].qryHistoryData where sectionOrder = #local.qryHistorySections.sectionOrder#
								</cfquery>									
								<historysection memberID="#arguments.memberID#" sectionTitle="#xmlFormat(local.qryHistorySections.sectionTitle)#" SectionOrder="#local.qryHistorySections.SectionOrder#" sectionID="#local.qryHistorySections.SectionOrder#">	
									<cfloop query="local.qryThisHistoryData">
									<history memberID="#arguments.memberID#" 
											sectionID="#local.qryHistorySections.SectionOrder#" 
											userDate="#xmlFormat(dateFormat(local.qryThisHistoryData.userDate, 'mm/dd/yyyy'))#" 
											userEndDate="#xmlFormat(dateFormat(local.qryThisHistoryData.userEndDate, 'mm/dd/yyyy'))#" 
											memberContent="#xmlFormat(local.qryThisHistoryData.memberContent)#" 
											HistoryCategory="#xmlFormat(local.qryThisHistoryData.HistoryCategory)#" 
											IncludeQtyAmt="#local.qryThisHistoryData.HistoryCategory#" 
											Quantity="#val(local.qryThisHistoryData.Quantity)#" 
											DollarAmt ="#val(local.qryThisHistoryData.DollarAmt)#"
											HistoryContent="#xmlFormat(local.qryThisHistoryData.HistoryContent)#"/>
									</cfloop>
								</historysection>
							</cfloop>
							</memberhistory>
						<cfelse>
							<memberhistory/>
						</cfif>	
						<cfif local.qryPaymentData.recordCount>
							<payments memberID="#arguments.memberID#">
							<cfloop query="local.qryPaymentData">
								<payment memberID="#arguments.memberID#" AccountName="#xmlFormat(local.qryPaymentData.accountName)#" paid="#local.qryPaymentData.paid#" />
							</cfloop>
							</payments>
						<cfelse>
							<payments/>
						</cfif>
						<cfif local.qryNoteData.recordCount>
							<notes memberID="#arguments.memberID#">
							<cfloop query="local.qryNoteSections">
								<cfquery name="local.qryThisNoteData" dbtype="query">
									select * from [local].qryNoteData where sectionOrder = #local.qryNoteSections.sectionOrder#
								</cfquery>									
								<notesection memberID="#arguments.memberID#" sectionTitle="#xmlFormat(local.qryNoteSections.sectionTitle)#" SectionOrder="#local.qryNoteSections.SectionOrder#" sectionID="#local.qryNoteSections.SectionOrder#">	
									<cfloop query="local.qryThisNoteData">
									<note memberID="#arguments.memberID#" 
											sectionID="#local.qryThisNoteData.SectionOrder#" 
											userDate="#xmlFormat(dateFormat(local.qryThisNoteData.userDate, 'mm/dd/yyyy'))#" 
											memberContent="#xmlFormat(local.qryThisNoteData.memberContent)#" 
											HistoryCategory="#xmlFormat(local.qryThisNoteData.HistoryCategory)#" 
											HistoryContent="#xmlFormat(local.qryThisNoteData.HistoryContent)#"/>
									</cfloop>
								</notesection>
							</cfloop>
							</notes>
						<cfelse>
							<notes/>
						</cfif>		
						<cfif local.qryRelationshipData.recordCount>
							<relationships memberID="#arguments.memberID#">
							<cfloop query="local.qryRelationshipSections">
								<cfquery name="local.qryThisRelationshipData" dbtype="query">
									select * from [local].qryRelationshipData where sectionOrder = #local.qryRelationshipSections.sectionOrder#
								</cfquery>									
								<relationshipsection memberID="#arguments.memberID#" sectionTitle="#xmlFormat(local.qryRelationshipSections.sectionTitle)#" SectionOrder="#local.qryRelationshipSections.SectionOrder#" sectionID="#local.qryRelationshipSections.SectionOrder#">	
									<cfloop query="local.qryThisRelationshipData">
									<relationship memberID="#arguments.memberID#" 
											sectionID="#local.qryThisRelationshipData.SectionOrder#" 
											userDate="#xmlFormat(dateFormat(local.qryThisRelationshipData.userDate, 'mm/dd/yyyy'))#" 
											memberContent="#xmlFormat(local.qryThisRelationshipData.memberContent)#" 
											HistoryCategory="#xmlFormat(local.qryThisRelationshipData.HistoryCategory)#" 
											HistoryContent="#xmlFormat(local.qryThisRelationshipData.HistoryContent)#"/>
									</cfloop>
								</relationshipsection>
							</cfloop>
							</relationships>
						<cfelse>
							<relationships/>
						</cfif>
					</member>
				</members>
				</cfoutput>			
			</cfsavecontent>

			<cffile action="write" output="#local.birtReportXML#" file="#local.returnStr.reportXMLFile#" nameconflict="overwrite">

			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.returnStr.success = false>
			</cfcatch>

		</cftry>	

		<cfreturn local.returnStr>
	</cffunction>	

	<cffunction name="runSubReport_subs" access="private" output="false" returntype="query">
		<cfargument name="itemUID" type="string" required="true">	
		<cfargument name="reportID" type="numeric" required="true">
		<cfargument name="otherXML" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfset local.frmasofbd = XMLSearch(arguments.otherXML,"string(/report/extra/frmasofbd/text())")>
		<cfset local.frmDRFrom = XMLSearch(arguments.otherXML,"string(/report/extra/frmdrfrom/text())")>
		<cfset local.frmDRTo = XMLSearch(arguments.otherXML,"string(/report/extra/frmdrto/text())")>
		<cfset local.frmIncludePastDue = XMLSearch(arguments.otherXML,"string(/report/extra/frmincludepastdue/text())")>
		<cfset local.frmIncludeDueNow = XMLSearch(arguments.otherXML,"string(/report/extra/frmincludeduenow/text())")>
		<cfset local.frmIncludeFutureDue = XMLSearch(arguments.otherXML,"string(/report/extra/frmincludefuturedue/text())")>
		<cfset local.currentReportSubRule = XMLSearch(arguments.otherXML,"/report/subrule")>	
		<cfset local.currentReportSubRule = trim(replaceNoCase(toString(local.currentReportSubRule[1]),'<?xml version="1.0" encoding="UTF-8"?>',''))>

		<cfquery name="local.qryReportInfo" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @reportID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.reportID#">, @reportName varchar(1000),
					@ruleID int, @toolTypeID int, @siteID int, @orgID int;

				select @ruleID = sr.ruleID, @toolTypeID = sr.toolTypeID, @siteID = sr.siteID, @orgID = dbo.fn_getOrgIDFromSiteID(sr.siteID)			
				from dbo.rpt_savedReports sr
				where reportID = @reportID;

				select top 1 @reportID as reportID, @ruleID as ruleID, @reportName as reportName, @toolTypeID as toolType, @siteID as siteID,
					@orgID as orgID, cast(<cfqueryparam cfsqltype="cf_sql_longvarchar" value="<report><extra /><fieldsets />#local.currentReportSubRule#</report>"> as xml) as otherXML
				from dbo.rpt_SavedReports as rsr;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.strSQLPrep = prepSQL(prepQry=local.qryReportInfo)>

		<cfset local.qryReport = qrySubReport(orgID=local.qryReportInfo.orgID, 
				strSQLPrep=local.strSQLPrep, frmAsOfBD=local.frmAsOfBD, frmDRFrom=local.frmDRFrom, frmDRTo=local.frmDRTo,
				frmIncludePastDue=local.frmIncludePastDue, frmIncludeDueNow=local.frmIncludeDueNow, 
				frmIncludeFutureDue=local.frmIncludeFutureDue)>
		
		<cfquery name="local.qryData" dbtype="query">
			select typeName + ' / ' + subscriptionName + ' / ' + rateName as subscription, SubscriptionStatus, subStartDate, subEndDate, billing_total, 
				billing_BilledPrice, invoices_dueInRange, paid_amount, memberID, <cfqueryparam cfsqltype="cf_sql_idstamp" value="#arguments.itemUID#"> as itemUID,
				statusPrimOrder, statusSecOrder
			from [local].qryReport.qryData
			order by statusPrimOrder desc, statusSecOrder, typeName, subscriptionName, subStartDate
		</cfquery>

		<cfreturn local.qryData>
	</cffunction>

	<cffunction name="runSubReport_events" access="private" output="false" returntype="query">
		<cfargument name="itemUID" type="string" required="true">	
		<cfargument name="reportID" type="numeric" required="true">
		<cfargument name="otherXML" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @reportID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.reportID#">, @ruleID int, @siteID int,
					@orgID int, @defaultLanguageID int;

				select @ruleID = sr.ruleID, @siteID = sr.siteID, @orgID = s.orgID, @defaultLanguageID = s.defaultLanguageID			
				from dbo.rpt_savedReports sr
				inner join dbo.sites as s on s.siteID = sr.siteID 
				where reportID = @reportID;

				DECLARE @tblE TABLE (eventID int PRIMARY KEY);

				IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
					DROP TABLE ##tmpVGRMembers;
				CREATE TABLE ##tmpVGRMembers (memberID int PRIMARY KEY);

				EXEC dbo.ams_RunVirtualGroupRuleV2 @orgID=@orgID, @ruleID=@ruleID;

				INSERT into @tblE (eventID)
				SELECT distinct e.eventid
				FROM dbo.ev_events as e
				INNER JOIN dbo.fn_intListToTable('0#XMLSearch(arguments.otherXML,"string(/report/extra/eidlist/text())")#',',') dg on dg.listitem = e.eventID
				WHERE e.siteID = @siteID;

				SELECT 
					<cfqueryparam cfsqltype="cf_sql_idstamp" value="#arguments.itemUID#"> as itemUID,
					m.memberID as reportMemberID, et.startTime, eventContent.contentTitle as eventTitle, r.attended, 
					creditsAwarded = REPLACE(REPLACE(( 
						SELECT cast(rc.creditValueAwarded as varchar(10)) + char(7) + isnull(ecast.ovTypeName,cat.typeName) AS [data()]
						FROM dbo.crd_requests as rc 
						INNER JOIN dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
						INNER JOIN dbo.crd_authoritySponsorTypes as ecast on ecast.astid = ect.astid
						INNER JOIN dbo.crd_authorityTypes as cat on cat.typeID = ecast.typeID
						WHERE rc.registrantID = r.registrantID
						ORDER BY isnull(ecast.ovTypeName,cat.typeName)
						FOR XML PATH ('')), ' ', ', '),'&##x07;',' '),
					ROW_NUMBER() OVER (ORDER BY m.memberID, et.startTime desc, eventContent.contentTitle) as reportOrder
				FROM dbo.ev_registrants as r
				INNER JOIN dbo.ev_registration as rn on rn.registrationID = r.registrationID AND rn.status = 'A' AND rn.siteID = @siteID
				INNER JOIN dbo.ev_events as e on e.eventID = rn.eventID and e.status <> 'D' and e.siteID = @siteID
				INNER JOIN @tblE as tblE on tblE.eventID = e.eventID
				INNER JOIN dbo.ev_times as et on et.timeID = e.defaultTimeID
				INNER JOIN dbo.ams_members as mReg on mReg.orgID = @orgID and mReg.memberID = r.memberID
				INNER JOIN dbo.ams_members as m on m.memberID = mReg.activeMemberID and m.orgID = @orgID and m.memberID = m.activeMemberID and m.status <> 'D'
				INNER JOIN ##tmpVGRMembers as tblM on tblM.memberID = m.memberID
				inner join dbo.cms_contentLanguages as eventContent on eventContent.siteID = @siteID and eventContent.contentID = e.eventContentID and eventContent.languageID = 1
				WHERE r.status = 'A'
				ORDER BY reportOrder;

				IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
					DROP TABLE ##tmpVGRMembers;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>	

		<cfreturn local.qryData>
	</cffunction>	

	<cffunction name="runSubReport_giving" access="private" output="false" returntype="query">
		<cfargument name="itemUID" type="string" required="true">	
		<cfargument name="reportID" type="numeric" required="true">
		<cfargument name="otherXML" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfset local.gllist = XMLSearch(arguments.otherXML,"string(/report/extra/givegllist/text())")>
		<cfset local.drfrom = XMLSearch(arguments.otherXML,"string(/report/extra/frmgivinghistoryfrom/text())")>
		<cfset local.drto = XMLSearch(arguments.otherXML,"string(/report/extra/frmgivinghistoryto/text())")>
		
		<cfif len(trim(local.gllist)) and len(trim(local.drfrom)) and len(trim(local.drto))>	

			<cfquery name="local.qryReportInfo" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				declare @reportID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.reportID#">, @reportName varchar(1000), @ruleID int,
					@toolTypeID int, @siteID int, @orgID int, @defaultLanguageID int;

				select @reportName = sr.reportName, @ruleID = sr.ruleID, @toolTypeID = sr.toolTypeID, @siteID = sr.siteID, @orgID = s.orgID,
					@defaultLanguageID = s.defaultLanguageID
				from dbo.rpt_savedReports sr
				inner join dbo.sites as s on s.siteID = sr.siteID 
				where reportID = @reportID;	

				select top 1 @reportID as reportID, @ruleID as ruleID, @reportName as reportName, @toolTypeID as toolType, @siteID as siteID, @orgID as orgID,			
					cast(<cfqueryparam cfsqltype="cf_sql_longvarchar" value="<report><fieldsets /><extra><frmgroupby>member</frmgroupby><frmreportview>detailed</frmreportview><gllist>#local.gllist#</gllist><frmdrfrom>#local.drfrom#</frmdrfrom><frmdrto>#local.drto#</frmdrto></extra></report>"> as xml) as otherXML
				from dbo.rpt_SavedReports as rsr;
			</cfquery>
	
			<cfset local.strSQLPrep = prepSQL(prepQry=local.qryReportInfo)>
			<cfset local.qryReport = qryGivingReport(strSQLPrep=local.strSQLPrep, orgID=local.qryReportInfo.orgID, DRFrom=local.drfrom, DRTo=local.drto)>
			
			<cfquery name="local.qryData" dbtype="query">
				select 
					<cfqueryparam cfsqltype="cf_sql_idstamp" value="#arguments.itemUID#"> as itemUID,
					reportMemberID, accountName, billedR, billedR1, billedR2, paidR, paidR1, paidR2, reportOrder, 
					'#local.DRFrom#' as startR, '#local.DRTo#' as endR, 
					'#dateformat(dateAdd("yyyy",-1,local.DRFrom),"m/d/yyyy")#' as startR1, '#dateformat(dateAdd("yyyy",-1,local.DRTo),"m/d/yyyy")#' as endR1,
					'#dateformat(dateAdd("yyyy",-2,local.DRFrom),"m/d/yyyy")#' as startR2, '#dateformat(dateAdd("yyyy",-2,local.DRTo),"m/d/yyyy")#' as endR2
				from [local].qryReport.qryData
				order by reportOrder
			</cfquery>

		<cfelse>			
			<cfset local.qryData = queryNew("itemUID, reportMemberID, accountName, billedR, billedR1, billedR2, paidR, paidR1, paidR2, reportOrder, startR, endR, startR1, endR1, startR2, endR2", 
											"VarChar, Integer, VarChar, Double, Double, Double, Double, Double, Double, Integer, Date, Date, Date, Date, Date, Date")>
		</cfif>
		
		<cfreturn local.qryData>
	</cffunction>

	<cffunction name="runSubReport_history" access="private" output="false" returntype="query">
		<cfargument name="itemUID" type="string" required="true">	
		<cfargument name="reportID" type="numeric" required="true">
		<cfargument name="otherXML" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.arrSections = XMLSearch(arguments.otherXML,"/report/extra/memhist/section")>
		
		<cfif not arrayLen(local.arrSections)>
			<cfset local.qryData = queryNew("itemUID, reportMemberID, sectionOrder, sectionTitle, includeQtyAmt, userDate, userEndDate, quantity, dollarAmt, historyCategory, MemberName, memberCompany, linkedMember, linkedMemberCompany, historyContent,memberContent,reportOrder", 
											"VarChar, Integer, Integer, VarChar, Double, Date, Date, Integer, Double, Integer, VarChar, VarChar, VarChar, VarChar, VarChar, VarChar, Integer")>
		<cfelse>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					declare @reportID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.reportID#">, @ruleID int, @siteID int, @orgID int, @defaultLanguageID int;

					select @ruleID = sr.ruleID, @siteID = sr.siteID, @orgID = s.orgID, @defaultLanguageID = s.defaultLanguageID			
					from dbo.rpt_savedReports sr
					inner join dbo.sites as s on s.siteID = sr.siteID 
					where reportID = @reportID;						

					IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
						DROP TABLE ##tmpVGRMembers;
					CREATE TABLE ##tmpVGRMembers (memberID int PRIMARY KEY);
		
					EXEC dbo.ams_RunVirtualGroupRuleV2 @orgID=@orgID, @ruleID=@ruleID;

					SELECT itemUID, reportMemberID, sectionOrder, sectionTitle, includeQtyAmt, userDate, userEndDate, quantity, dollarAmt, historyCategory, MemberName, memberCompany, 
						linkedMember, linkedMemberCompany, historyContent,
						memberContent = replace(MemberName,'&','&amp;') + 
							case
								when memberCompany is not null  and linkedMember is not null and linkedMemberCompany is null then
									'<br/>' + replace(memberCompany,'&','&amp;') + '<br/><em>' + replace(linkedMember,'&','&amp;') + '</em>'
								when memberCompany is not null  and linkedMember is not null and linkedMemberCompany is not null then
									'<br/>' + replace(memberCompany,'&','&amp;') + '<br/>    <em>' + replace(linkedMember,'&','&amp;') + '</em><br/><em>' + replace(linkedMemberCompany,'&','&amp;') + '</em>'
								when memberCompany is null  and linkedMember is not null and linkedMemberCompany is null then
									'<br/><em>' + replace(linkedMember,'&','&amp;')  + '</em>'
								when memberCompany is null  and linkedMember is not null and linkedMemberCompany is not null then
									'<br/><em>' + replace(linkedMember,'&','&amp;')  + '</em><br/><em>' + replace(linkedMemberCompany,'&','&amp;') + '</em>'
								else
								''
							end,															
						ROW_NUMBER() OVER(ORDER BY sectionOrder, sectionTitle, userDate desc) as reportOrder
					FROM (
						<cfset local.thisSectionCount = 0>
						<cfloop array="#local.arrSections#" index="local.thisSection">
							<cfset local.thisSectionCount = local.thisSectionCount + 1>
							SELECT '#local.thisSection.t.xmltext#' as sectionTitle, #numberformat("000#local.thisSectionCount#")# as sectionOrder, 
								#val(local.thisSection.i.xmltext)# as includeQtyAmt, 
								mActive.memberID as reportMemberID, mh.userDate, mh.userEndDate, mh.quantity, mh.dollarAmt, 
								cP.categoryName + isnull(char(10) + cg2.categoryName,'') as historyCategory,
								mActive.lastName + ', ' + mActive.firstName + ' (' + mActive.memberNumber + ')' as MemberName, mActive.company as MemberCompany, 
								mLinkActive.lastName + ', ' + mLinkActive.firstName + ' (' + mLinkActive.memberNumber + ')' as linkedMember, mLinkActive.company as linkedMemberCompany, 
								mh.description as historyContent,
								<cfqueryparam cfsqltype="cf_sql_idstamp" value="#arguments.itemUID#"> as itemUID
							FROM dbo.ams_memberHistory as mh
							INNER JOIN dbo.cms_categories as cP on cP.categoryID = mh.categoryID
							INNER JOIN dbo.cms_categoryTrees as cPt on cPt.categoryTreeID = cP.categoryTreeID
							INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberID = mh.memberID
							INNER JOIN dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID 
							LEFT OUTER JOIN dbo.cms_categories cg2 on cg2.categoryID = mh.subCategoryID
							LEFT OUTER JOIN dbo.ams_members as mLink 
								INNER JOIN dbo.ams_members as mLinkActive on mLinkActive.orgID = @orgID and mLinkActive.memberID = mLink.activeMemberID
								on mLink.orgID = @orgID and mLink.memberID = mh.linkMemberID
							INNER JOIN ##tmpVGRMembers as tblM on tblM.memberID = mActive.memberID OR tblM.memberID = mLinkActive.memberID
							WHERE mh.siteID = @siteID
							AND cPt.siteID = @siteID
							and mh.typeID = 1
							and mh.status = 'A'
							and (cP.categoryID in (0#local.thisSection.c.xmltext#)  or cg2.categoryID in (0#local.thisSection.c.xmltext#))
							<cfif len(local.thisSection.df.xmlText) and len(local.thisSection.dt.xmlText)>
								and mh.userDate between '#local.thisSection.df.xmlText#' and '#local.thisSection.dt.xmlText# 23:59:59.997'
							<cfelseif len(local.thisSection.df.xmlText)>
								and mh.userDate >= '#local.thisSection.df.xmlText#'
							<cfelseif len(local.thisSection.dt.xmlText)>
								and mh.userDate <= '#local.thisSection.dt.xmlText# 23:59:59.997'
							</cfif>
							<cfif isDefined("local.thisSection.edf.xmlText") and isDefined("local.thisSection.edt.xmlText") and len(local.thisSection.edf.xmlText) and len(local.thisSection.edt.xmlText)>
								and mh.userEndDate between '#local.thisSection.edf.xmlText#' and '#local.thisSection.edt.xmlText# 23:59:59.997'
							<cfelseif isDefined("local.thisSection.edf.xmlText") and len(local.thisSection.edf.xmlText)>
								and mh.userEndDate >= '#local.thisSection.edf.xmlText#'
							<cfelseif isDefined("local.thisSection.edt.xmlText") and len(local.thisSection.edt.xmlText)>
								and mh.userEndDate <= '#local.thisSection.edt.xmlText# 23:59:59.997'
							</cfif>
							
							<cfif local.thisSectionCount lt arrayLen(local.arrSections)>
								union all	
							</cfif>
						</cfloop>
					) as tmp
					ORDER BY historyCategory, userDate;

					IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
						DROP TABLE ##tmpVGRMembers;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		</cfif>

		<cfreturn local.qryData>
	</cffunction>	

	<cffunction name="runSubReport_payments" access="private" output="false" returntype="query">
		<cfargument name="itemUID" type="string" required="true">	
		<cfargument name="reportID" type="numeric" required="true">
		<cfargument name="otherXML" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfset local.gllist = XMLSearch(arguments.otherXML,"string(/report/extra/paygllist/text())")>
		<cfset local.drfrom = XMLSearch(arguments.otherXML,"string(/report/extra/frmpaymentsummaryfrom/text())")>
		<cfset local.drto = XMLSearch(arguments.otherXML,"string(/report/extra/frmpaymentsummaryto/text())")>
		<cfset local.frmlinkalloctype = XMLSearch(arguments.otherXML,"string(/report/extra/frmlinkalloctype/text())")>
		
		<cfif len(trim(local.gllist)) and len(trim(local.drfrom)) and len(trim(local.drto))>	

			<cfquery name="local.qryReportInfo" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				declare @reportID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.reportID#">, @reportName varchar(1000), @ruleID int,
					@toolTypeID int, @siteID int, @orgID int, @defaultLanguageID int;

				select @reportName = sr.reportName, @ruleID = sr.ruleID, @toolTypeID = sr.toolTypeID, @siteID = sr.siteID, @orgID = s.orgID, @defaultLanguageID = s.defaultLanguageID
				from dbo.rpt_savedReports sr
				inner join dbo.sites as s on s.siteID = sr.siteID 
				where reportID = @reportID;	

				select top 1 @reportID as reportID, @ruleID as ruleID, @reportName as reportName, @toolTypeID as toolType, 
					@siteID as siteID, @orgID as orgID,			
					cast(<cfqueryparam cfsqltype="cf_sql_longvarchar" value="<report><fieldsets /><extra><frmreportorder>group</frmreportorder><frmreportview>memdetail</frmreportview><frmlinkalloctype>#local.frmlinkalloctype#</frmlinkalloctype><gllist>#local.gllist#</gllist><frmdrfrom>#local.drfrom#</frmdrfrom><frmdrto>#local.drto#</frmdrto></extra></report>"> as xml) as otherXML
				from dbo.rpt_SavedReports as rsr;
			</cfquery>

			<cfset local.strSQLPrep = prepSQL(prepQry=local.qryReportInfo)>
			<cfset local.qryReport = qryPaymentReport(strSQLPrep=local.strSQLPrep, orgID=local.qryReportInfo.orgID, 
					frmDRFrom=local.drfrom, frmDRTo=local.drto, frmLinkAllocType=local.frmLinkAllocType)>
			
			<cfquery name="local.qryData" dbtype="query">
				select 
					<cfqueryparam cfsqltype="cf_sql_idstamp" value="#arguments.itemUID#"> as itemUID,
					reportMemberID, accountName, allocatedAmount as Paid, reportOrder
				from [local].qryReport.qryData
				order by reportOrder
			</cfquery>
			
		<cfelse>			
			<cfset local.qryData = queryNew("itemUID, reportMemberID,accountName,Paid,reportOrder", "VarChar, Integer, VarChar, Double, Integer")>
		</cfif>

		<cfreturn local.qryData>
	</cffunction>

	<cffunction name="runSubReport_relationships" access="private" output="false" returntype="query">
		<cfargument name="itemUID" type="string" required="true">	
		<cfargument name="reportID" type="numeric" required="true">
		<cfargument name="otherXML" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.arrSections = XMLSearch(arguments.otherXML,"/report/extra/relationship/section")>
		
		<cfif not arrayLen(local.arrSections)>
			<cfset local.qryData = queryNew("itemUID, reportMemberID, sectionOrder, sectionTitle, userDate, historyCategory, MemberName, memberCompany, linkedMember, linkedMemberCompany, historyContent,memberContent,reportOrder", 
				"VarChar, Integer, Integer, VarChar, Date, Integer, VarChar, VarChar, VarChar, VarChar, VarChar, VarChar, Integer")>
		<cfelse>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					declare @reportID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.reportID#">, @ruleID int, @siteID int, @orgID int, @defaultLanguageID int;

					select @ruleID = sr.ruleID, @siteID = sr.siteID, @orgID = s.orgID, @defaultLanguageID = s.defaultLanguageID
					from dbo.rpt_savedReports sr
					inner join dbo.sites as s on s.siteID = sr.siteID 
					where reportID = @reportID;

					IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
						DROP TABLE ##tmpVGRMembers;
					CREATE TABLE ##tmpVGRMembers (memberID int PRIMARY KEY);

					EXEC dbo.ams_RunVirtualGroupRuleV2 @orgID=@orgID, @ruleID=@ruleID;
			
					SELECT itemUID, reportMemberID, sectionOrder, sectionTitle, userDate, historyCategory, MemberName, memberCompany, 
						linkedMember, linkedMemberCompany, historyContent,
						memberContent = replace(MemberName,'&','&amp;') + 
							case
								when memberCompany is not null  and linkedMember is not null and linkedMemberCompany is null then
									'<br/>' + replace(memberCompany,'&','&amp;') + '<br/><em>' + replace(linkedMember,'&','&amp;') + '</em>'
								when memberCompany is not null  and linkedMember is not null and linkedMemberCompany is not null then
									'<br/>' + replace(memberCompany,'&','&amp;') + '<br/><em>' + replace(linkedMember,'&','&amp;') + '</em><br/><em>' + replace(linkedMemberCompany,'&','&amp;') + '</em>'
								when memberCompany is null  and linkedMember is not null and linkedMemberCompany is null then
									'<br/><em>' + replace(linkedMember,'&','&amp;')  + '</em>'
								when memberCompany is null  and linkedMember is not null and linkedMemberCompany is not null then
									'<br/><em>' + replace(linkedMember,'&','&amp;')  + '</em><br/><em>' + replace(linkedMemberCompany,'&','&amp;') + '</em>'
								else
								''
							end,				
						ROW_NUMBER() OVER(ORDER BY sectionOrder, sectionTitle, userDate desc) as reportOrder
					FROM (
						<cfset local.thisSectionCount = 0>
						<cfloop array="#local.arrSections#" index="local.thisSection">
							<cfset local.thisSectionCount = local.thisSectionCount + 1>
							SELECT '#local.thisSection.t.xmltext#' as sectionTitle, #numberformat("000#local.thisSectionCount#")# as sectionOrder, 
								mActive.memberID as reportMemberID, mh.userDate, 
								cP.categoryName + isnull(char(10) + cg2.categoryName,'') as historyCategory,
								mActive.lastName + ', ' + mActive.firstName + ' (' + mActive.memberNumber + ')' as MemberName, mActive.company as MemberCompany, 
								mLinkActive.lastName + ', ' + mLinkActive.firstName + ' (' + mLinkActive.memberNumber + ')' as linkedMember, mLinkActive.company as linkedMemberCompany, 
								mh.description as historyContent, <cfqueryparam cfsqltype="cf_sql_idstamp" value="#arguments.itemUID#"> as itemUID
							FROM dbo.ams_memberHistory as mh
							INNER JOIN dbo.cms_categories as cP on cP.categoryID = mh.categoryID
							INNER JOIN dbo.cms_categoryTrees as cPt on cPt.categoryTreeID = cP.categoryTreeID
							INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberID = mh.memberID
							INNER JOIN dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
							LEFT OUTER JOIN dbo.cms_categories cg2 on cg2.categoryID = mh.subCategoryID
							LEFT OUTER JOIN dbo.ams_members as mLink 
								INNER JOIN dbo.ams_members as mLinkActive on mLinkActive.orgID = @orgID and mLinkActive.memberID = mLink.activeMemberID
								on mLink.orgID = @orgID and mLink.memberID = mh.linkMemberID
							INNER JOIN ##tmpVGRMembers as tblM on tblM.memberID = mActive.memberID OR tblM.memberID = mLinkActive.memberID
							WHERE mh.siteID = @siteID
							AND cPt.siteID = @siteID
							and mh.typeID = 2
							and mh.status = 'A'
							and ((cP.categoryID in (0#local.thisSection.c.xmltext#) and cg2.categoryID is null) or cg2.categoryID in (0#local.thisSection.c.xmltext#))
							<cfif len(local.thisSection.df.xmlText) and len(local.thisSection.dt.xmlText)>
								and mh.userDate between '#local.thisSection.df.xmlText#' and '#local.thisSection.dt.xmlText# 23:59:59.997'
							<cfelseif len(local.thisSection.df.xmlText)>
								and mh.userDate >= '#local.thisSection.df.xmlText#'
							<cfelseif len(local.thisSection.dt.xmlText)>
								and mh.userDate <= '#local.thisSection.dt.xmlText# 23:59:59.997'
							</cfif>
							
							<cfif local.thisSectionCount lt arrayLen(local.arrSections)>
								union all	
							</cfif>
						</cfloop>
					) as tmp
					order by reportOrder;

					IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
						DROP TABLE ##tmpVGRMembers;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>	
		</cfif>

		<cfreturn local.qryData>
	</cffunction>	

	<cffunction name="runSubReport_notes" access="private" output="false" returntype="query">
		<cfargument name="itemUID" type="string" required="true">	
		<cfargument name="reportID" type="numeric" required="true">
		<cfargument name="otherXML" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.arrSections = XMLSearch(arguments.otherXML,"/report/extra/notes/section")>
		
		<cfif not arrayLen(local.arrSections)>
			<cfset local.qryData = queryNew("itemUID, reportMemberID, sectionOrder, sectionTitle, userDate, historyCategory, MemberName, memberCompany, linkedMember, linkedMemberCompany, historyContent,memberContent,reportOrder", 
											"VarChar, Integer, Integer, VarChar, Date, Integer, VarChar, VarChar, VarChar, VarChar, VarChar, VarChar, Integer")>
		<cfelse>		
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					declare @reportID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.reportID#">, @ruleID int, @siteID int, @orgID int, @defaultLanguageID int;

					select @ruleID = sr.ruleID, @siteID = sr.siteID, @orgID = s.orgID, @defaultLanguageID = s.defaultLanguageID
					from dbo.rpt_savedReports sr
					inner join dbo.sites as s on s.siteID = sr.siteID 
					where reportID = @reportID;

					IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
						DROP TABLE ##tmpVGRMembers;
					CREATE TABLE ##tmpVGRMembers (memberID int PRIMARY KEY);

					EXEC dbo.ams_RunVirtualGroupRuleV2 @orgID=@orgID, @ruleID=@ruleID;
			
					SELECT itemUID, reportMemberID, sectionOrder, sectionTitle, userDate, historyCategory, MemberName, memberCompany, 
						linkedMember, linkedMemberCompany, historyContent,
						memberContent = replace(MemberName,'&','&amp;') + 
							case
								when memberCompany is not null  and linkedMember is not null and linkedMemberCompany is null then
									'<br/>' + replace(memberCompany,'&','&amp;') + '<br/><em>' + replace(linkedMember,'&','&amp;') + '</em>'
								when memberCompany is not null  and linkedMember is not null and linkedMemberCompany is not null then
									'<br/>' + replace(memberCompany,'&','&amp;') + '<br/><em>' + replace(linkedMember,'&','&amp;') + '</em><br/><em>' + replace(linkedMemberCompany,'&','&amp;') + '</em>'
								when memberCompany is null  and linkedMember is not null and linkedMemberCompany is null then
									'<br/><em>' + replace(linkedMember,'&','&amp;')  + '</em>'
								when memberCompany is null  and linkedMember is not null and linkedMemberCompany is not null then
									'<br/><em>' + replace(linkedMember,'&','&amp;')  + '</em><br/><em>' + replace(linkedMemberCompany,'&','&amp;') + '</em>'
								else
								''
							end,				
						ROW_NUMBER() OVER(ORDER BY sectionOrder, sectionTitle, userDate desc) as reportOrder
					FROM (
						<cfset local.thisSectionCount = 0>
						<cfloop array="#local.arrSections#" index="local.thisSection">
							<cfset local.thisSectionCount = local.thisSectionCount + 1>
							SELECT '#local.thisSection.t.xmltext#' as sectionTitle, #numberformat("000#local.thisSectionCount#")# as sectionOrder, 
								mActive.memberID as reportMemberID, mh.userDate, 
								cP.categoryName + isnull(char(10) + cg2.categoryName,'') as historyCategory,
								mActive.lastName + ', ' + mActive.firstName + ' (' + mActive.memberNumber + ')' as MemberName, mActive.company as MemberCompany, 
								mLinkActive.lastName + ', ' + mLinkActive.firstName + ' (' + mLinkActive.memberNumber + ')' as linkedMember, mLinkActive.company as linkedMemberCompany, 
								mh.description as historyContent, <cfqueryparam cfsqltype="cf_sql_idstamp" value="#arguments.itemUID#"> as itemUID
							FROM dbo.ams_memberHistory as mh
							INNER JOIN dbo.cms_categories as cP on cP.categoryID = mh.categoryID
							INNER JOIN dbo.cms_categoryTrees as cPt on cPt.categoryTreeID = cP.categoryTreeID
							INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberID = mh.memberID
							INNER JOIN dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
							LEFT OUTER JOIN dbo.cms_categories cg2 on cg2.categoryID = mh.subCategoryID
							LEFT OUTER JOIN dbo.ams_members as mLink 
								INNER JOIN dbo.ams_members as mLinkActive on mLinkActive.orgID = @orgID and mLinkActive.memberID = mLink.activeMemberID
								on mLink.orgID = @orgID and mLink.memberID = mh.linkMemberID
							INNER JOIN ##tmpVGRMembers as tblM on tblM.memberID = mActive.memberID OR tblM.memberID = mLinkActive.memberID
							WHERE mh.siteID = @siteID
							AND cPt.siteID = @siteID
							and mh.typeID = 3
							and mh.status = 'A'
							<!--- Ticket 8433085 Default filter says all categories if only date provided --->
							<cfif len(local.thisSection.c.xmlText) and local.thisSection.dt.xmlText neq "0">
								and ((cP.categoryID in (0#local.thisSection.c.xmltext#) and cg2.categoryID is null) or cg2.categoryID in (0#local.thisSection.c.xmltext#))
							</cfif>
							<cfif len(local.thisSection.df.xmlText) and len(local.thisSection.dt.xmlText)>
								and mh.userDate between '#local.thisSection.df.xmlText#' and '#local.thisSection.dt.xmlText# 23:59:59.997'
							<cfelseif len(local.thisSection.df.xmlText)>
								and mh.userDate >= '#local.thisSection.df.xmlText#'
							<cfelseif len(local.thisSection.dt.xmlText)>
								and mh.userDate <= '#local.thisSection.dt.xmlText# 23:59:59.997'
							</cfif>
							
							<cfif local.thisSectionCount lt arrayLen(local.arrSections)>
								union all	
							</cfif>
						</cfloop>
					) as tmp
					ORDER BY reportOrder;

					IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
						DROP TABLE ##tmpVGRMembers;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>	
		</cfif>

		<cfreturn local.qryData>
	</cffunction>	

	<cffunction name="qrySubReport" access="public" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="strSQLPrep" type="struct" required="true">
		<cfargument name="frmAsOfBD" type="date" required="true">
		<cfargument name="frmDRFrom" type="date" required="true">
		<cfargument name="frmDRTo" type="date" required="true">
		<cfargument name="frmIncludePastDue" type="string" required="true">
		<cfargument name="frmIncludeDueNow" type="string" required="true">
		<cfargument name="frmIncludeFutureDue" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>
		<cfset local.arrLimitSubs = arrayNew(1)>
		<cfset local.limitSubs = "">

		<cfset local.tempTableName = "rpt#getTickCount()#">	

		<cfif arguments.frmIncludePastDue EQ "1">
			<cfset arrayAppend(local.arrLimitSubs, "tmp2b.[Amount Past Due] > 0")>
		</cfif>
		<cfif arguments.frmIncludeDueNow EQ "1">
			<cfset arrayAppend(local.arrLimitSubs, "tmp2b.[Amount Due Now] > 0")>
		</cfif>
		<cfif arguments.frmIncludeFutureDue EQ "1">
			<cfset arrayAppend(local.arrLimitSubs, "tmp2b.[Amount Due in Future] > 0")>
		</cfif>
		<cfif arrayLen(local.arrLimitSubs)>
			<cfset local.opts = ArrayToList(local.arrLimitSubs, " OR ")>
			<cfset local.limitSubs = " where 1=1 and ( #local.opts# )">
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.strReturn.qryData" result="local.strReturn.qryDataResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
				IF OBJECT_ID('tempdb..###local.tempTableName#_2') IS NOT NULL
					DROP TABLE ###local.tempTableName#_2;
				IF OBJECT_ID('tempdb..###local.tempTableName#_2b') IS NOT NULL
					DROP TABLE ###local.tempTableName#_2b;
				IF OBJECT_ID('tempdb..###local.tempTableName#_3') IS NOT NULL
					DROP TABLE ###local.tempTableName#_3;
				IF OBJECT_ID('tempdb..#####local.tempTableName#_4') IS NOT NULL
					DROP TABLE #####local.tempTableName#_4;
				IF OBJECT_ID('tempdb..###local.tempTableName#_5') IS NOT NULL
					DROP TABLE ###local.tempTableName#_5;
				IF OBJECT_ID('tempdb..###local.tempTableName#_6') IS NOT NULL
					DROP TABLE ###local.tempTableName#_6;
				IF OBJECT_ID('tempdb..##subBilled') IS NOT NULL
		      		DROP TABLE ##subBilled;
				IF OBJECT_ID('tempdb..###local.tempTableName#_summary') IS NOT NULL
					DROP TABLE ###local.tempTableName#_summary;

				<cfif len(arguments.strSQLPrep.ruleSQL)>#PreserveSingleQuotes(arguments.strSQLPrep.ruleSQL)#</cfif>

				DECLARE @mc_orgID int = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">;

				select s.subscriberID, m.memberid, m.lastname, m.firstname, m.membernumber, m.Company, 
					stypes.typeName, rootT.typeName as rootTypeName, sub.subscriptionName, subrate.rateName, s.subStartDate, s.subEndDate, 
					convert(varchar(10),s.subStartDate,101) as [Start Date], convert(varchar(10),s.subEndDate,101) as [End Date], 
					substatus.statusCode as SubscriptionStatusCode, substatus.statusName as SubscriptionStatus, 
					subpaystatus.statusName as PaymentStatus, s.subscriberPath,
					freq.frequencyID, freq.frequencyName, freq.frequencyShortName,
					cast(null as varchar(300)) as datacolprefix
				into ###local.tempTableName#_2
				from dbo.sub_subscribers as s
				inner join dbo.sub_subscriptions as sub on sub.orgID = @mc_orgID and sub.subscriptionID = s.subscriptionID
				inner join dbo.sub_types stypes on stypes.typeID = sub.typeID
				inner join dbo.sub_statuses as substatus on substatus.statusID = s.statusID
				inner join dbo.sub_paymentstatuses as subpaystatus on subpaystatus.statusID = s.paymentStatusID
				inner join dbo.sub_rateFrequencies subrf on subrf.RFID = s.RFID
				inner join dbo.sub_frequencies freq on freq.frequencyID = subrf.frequencyID		
				inner join dbo.sub_rates subrate on subrate.rateID = subrf.rateID
				inner join dbo.sub_subscribers as rootS on rootS.orgID = @mc_orgID and rootS.subscriberID = s.rootSubscriberID
				inner join dbo.sub_subscriptions as rootSub on rootSub.orgID = @mc_orgID and rootSub.subscriptionID = rootS.subscriptionID
				inner join dbo.sub_types as rootT on rootT.typeID = rootSub.typeID 
				inner join dbo.ams_members as m2 on m2.orgID = @mc_orgID and m2.memberid = s.memberID
				inner join dbo.ams_members as m on m.orgID = @mc_orgID and m.memberid = m2.activeMemberID and m.status <> 'D'
				<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
				where s.orgID = @mc_orgID;
				
				-- index for the member data inclusion
				CREATE NONCLUSTERED INDEX IX_memberid ON ###local.tempTableName#_2 (memberID);

				/* ************************************ */
				/* base data done. get accounting data. */
				/* ************************************ */
				CREATE TABLE ###local.tempTableName#_2b (
					memberID int null,
					subscriberID int null,
					rootTypeName varchar(100) null,
					billing_BilledPrice decimal (18,2) not null DEFAULT 0,
					billing_total decimal (18,2) not null DEFAULT 0,
					invoices_daysPastDue int not null DEFAULT 0,
					invoices_dueBeforeRange decimal (18,2) not null DEFAULT 0,
					invoices_dueInRange decimal (18,2) not null DEFAULT 0,
					invoices_dueAfterRange decimal (18,2) not null DEFAULT 0,
					invoices_dueBeforeAndInRange decimal (18,2) not null DEFAULT 0,
					invoices_dueBeforeAndInAndAfterRange decimal (18,2) not null DEFAULT 0,
					rowNum int not null default 0,
					datacolprefix varchar(300) null,
					paid_amount decimal (18,2) not null DEFAULT 0
				);
				insert into ###local.tempTableName#_2b (memberID, subscriberID, rootTypeName)
				select memberID, subscriberID, rootTypeName
				from ###local.tempTableName#_2;

				-- accounting data
				CREATE TABLE ###local.tempTableName#_3 (subscriberID int, invoiceID int, dateDue datetime, subInvAmount decimal(18,2), daysPastDue int);
				INSERT INTO ###local.tempTableName#_3
				EXEC dbo.sub_report_SubscriptionBilling @orgID=@mc_orgID, @tblName='###local.tempTableName#_2', @asOfDate='#arguments.frmAsOfBD#', @invoiceDueLower='#arguments.frmDRFrom#';

				-- billing
				CREATE TABLE ##subBilled (rootSubscriberID int, selectedSubscriberID int, subBilledPrice decimal(18,2) DEFAULT(0));
				insert into ##subBilled (rootSubscriberID, selectedSubscriberID, subBilledPrice)
				select distinct s.rootSubscriberID as rootSubscriberID, sAll.subscriberID as selectedSubscriberID, 0 as subBilledPrice
				from ###local.tempTableName#_2 as tmpsubs
				inner join dbo.sub_subscribers as s on s.orgID = @mc_orgID and s.subscriberID = tmpsubs.subscriberID
				inner join dbo.sub_subscribers as sAll on sAll.orgID = @mc_orgID and sAll.rootSubscriberID = s.rootSubscriberID
				inner join dbo.sub_statuses as sAllstat on sAllstat.statusID = sAll.statusID
				where sAllstat.statusCode <> 'D';

				update tmp
				set tmp.subBilledPrice = tmp2.subBilledPrice
				from ##subBilled as tmp
				inner join (
					select innertmp.rootSubscriberID, 
						isnull(sum(tsFull.cache_amountAfterAdjustment),	
							sum(case 
								when s.PCFree = 1 then convert(decimal(18,2),0) 
								else 
									case 
									when s.modifiedRate is not null then s.modifiedRate 
									else s.lastPrice 
									end
								end)) as subBilledPrice
					from ##subBilled as innertmp
					inner join dbo.sub_subscribers as s on s.orgID = @mc_orgID and s.subscriberID = innertmp.selectedSubscriberID
					left outer join dbo.tr_applications as ta 
						cross apply dbo.fn_tr_transactionSalesWithDIT(@mc_orgID,ta.transactionID) as tsFull
						on ta.orgID = @mc_orgID and ta.itemID = s.subscriberID and ta.applicationTypeID = 17
					group by innertmp.rootSubscriberID
				) as tmp2 on tmp2.rootSubscriberID = tmp.rootSubscriberID
				where tmp.rootSubscriberID = tmp.selectedSubscriberID
				OPTION(RECOMPILE);

				CREATE NONCLUSTERED INDEX IX_subBilled ON ##subBilled (selectedSubscriberID);

				update s
				set s.billing_BilledPrice = isnull(tb.subBilledPrice,0)
				from ###local.tempTableName#_2b as s
				inner join ###local.tempTableName#_2 as s2 on s2.subscriberID = s.subscriberID
				inner join ##subBilled as tb on tb.selectedSubscriberID = s.subscriberID and s2.subscriptionStatusCode = 'O';

				update s
				set s.billing_total = isnull(tb.subBilledPrice,0)
				from ###local.tempTableName#_2b as s
				inner join ##subBilled as tb on tb.selectedSubscriberID = s.subscriberID;

				-- aging
				update s
				set s.invoices_daysPastDue = tmp.daysPastDue
				from ###local.tempTableName#_2b as s
				inner join (
					select subscriberID, max(daysPastDue) as daysPastDue
					from ###local.tempTableName#_3
					group by subscriberID
				) as tmp on tmp.subscriberid = s.subscriberid;

				-- invoices due before range
				update s
				set s.invoices_dueBeforeRange = tmp.subInvAmount
				from ###local.tempTableName#_2b as s
				inner join (
					select subscriberID, sum(subInvAmount) as subInvAmount
					from ###local.tempTableName#_3
					where dateDue < '#arguments.frmDRFrom#'
					group by subscriberID
				) as tmp on tmp.subscriberid = s.subscriberid;

				-- invoices due in range
				update s
				set s.invoices_dueInRange = tmp.subInvAmount
				from ###local.tempTableName#_2b as s
				inner join (
					select subscriberID, sum(subInvAmount) as subInvAmount
					from ###local.tempTableName#_3
					where dateDue between '#arguments.frmDRFrom#' and '#arguments.frmDRTo# 23:59:59.997'
					group by subscriberID
				) as tmp on tmp.subscriberid = s.subscriberid;

				-- invoices due after range
				update s
				set s.invoices_dueAfterRange = tmp.subInvAmount
				from ###local.tempTableName#_2b as s
				inner join (
					select subscriberID, sum(subInvAmount) as subInvAmount
					from ###local.tempTableName#_3
					where dateDue > '#arguments.frmDRTo# 23:59:59.997'
					group by subscriberID
				) as tmp on tmp.subscriberid = s.subscriberid;

				-- totals by member
				insert into ###local.tempTableName#_2b (memberID, subscriberID, rootTypeName, billing_BilledPrice, billing_total, invoices_daysPastDue, 
					invoices_dueBeforeRange, invoices_dueInRange, invoices_dueAfterRange)
				select memberID, null, null, sum(billing_BilledPrice), sum(billing_total), max(invoices_daysPastDue), 
					sum(invoices_dueBeforeRange), sum(invoices_dueInRange), sum(invoices_dueAfterRange)
				from ###local.tempTableName#_2b
				group by memberID;

				-- calc columns
				update ###local.tempTableName#_2b
				set invoices_dueBeforeAndInRange = invoices_dueBeforeRange + invoices_dueInRange,
					invoices_dueBeforeAndInAndAfterRange = invoices_dueBeforeRange + invoices_dueInRange + invoices_dueAfterRange,
					paid_amount = billing_total - invoices_dueBeforeRange - invoices_dueInRange  - invoices_dueAfterRange;
				
				/* ****************************************************************************	*/
				/* At this point we have:														*/
				/* 	tmp 	memberid and fieldset fields										*/
				/* 	tmp_2	raw data by subscriberID											*/
				/* 	tmp_2b	subscriberID, memberID, root Type, accounting, and datacolprefix	*/
				/* 	tmp_3	raw billing info													*/
				/* **************************************************************************** */

				select distinct tmp2.subscriberID, tmp2.lastname, tmp2.firstname, tmp2.membernumber, tmp2.Company, 
					tmp2.typeName, tmp2.subscriptionName, tmp2.rateName, tmp2.subStartDate, tmp2.subEndDate, 
					tmp2.SubscriptionStatus, tmp2.frequencyName, tmp2b.billing_BilledPrice, tmp2b.billing_total, 
					case 
						when tmp2.SubscriptionStatus = 'Active' then 1 else 0
					end as statusPrimOrder,						
					case 
						when tmp2.SubscriptionStatus = 'Active' then 1 
						when tmp2.SubscriptionStatus = 'Accepted' then 2 
						when tmp2.SubscriptionStatus = 'Billed' then 3
						when tmp2.SubscriptionStatus = 'Renewal Not Sent' then 4
						when tmp2.SubscriptionStatus = 'Inactive' then 5
						when tmp2.SubscriptionStatus = 'Offer Expired' then 6
						when tmp2.SubscriptionStatus = 'Expired' then 7 
						when tmp2.SubscriptionStatus = 'Deleted' then 8 
						else 9
					end as statusSecOrder,					 
					tmp2b.invoices_daysPastDue, tmp2b.invoices_dueBeforeRange, tmp2b.invoices_dueInRange, tmp2b.paid_amount,
					tmp2b.invoices_dueAfterRange, tmp2b.invoices_dueBeforeAndInRange, tmp2b.invoices_dueBeforeAndInAndAfterRange,
					tmp2b.memberID
				from ###local.tempTableName#_2 as tmp2
				inner join ###local.tempTableName#_2b as tmp2b on tmp2b.subscriberID = tmp2.subscriberID
				<cfif len(local.limitSubs)>
					#replaceNoCase(replaceNoCase(replaceNoCase(local.limitSubs,"[Amount Past Due]","invoices_dueBeforeRange"),"[Amount Due Now]","invoices_dueInRange"),"[Amount Due in Future]","invoices_dueAfterRange")#
				</cfif>
				order by statusPrimOrder desc, statusSecOrder, tmp2.lastname, tmp2.firstname, tmp2.membernumber, tmp2.typeName, tmp2.subscriptionName, tmp2.subStartDate DESC;

				IF OBJECT_ID('tempdb..###local.tempTableName#_2') IS NOT NULL
					DROP TABLE ###local.tempTableName#_2;
				IF OBJECT_ID('tempdb..###local.tempTableName#_2b') IS NOT NULL
					DROP TABLE ###local.tempTableName#_2b;
				IF OBJECT_ID('tempdb..###local.tempTableName#_3') IS NOT NULL
					DROP TABLE ###local.tempTableName#_3;
				IF OBJECT_ID('tempdb..#####local.tempTableName#_4') IS NOT NULL
					DROP TABLE #####local.tempTableName#_4;
				IF OBJECT_ID('tempdb..###local.tempTableName#_5') IS NOT NULL
					DROP TABLE ###local.tempTableName#_5;
				IF OBJECT_ID('tempdb..###local.tempTableName#_6') IS NOT NULL
					DROP TABLE ###local.tempTableName#_6;
				IF OBJECT_ID('tempdb..##subBilled') IS NOT NULL
		      		DROP TABLE ##subBilled;
				IF OBJECT_ID('tempdb..###local.tempTableName#_summary') IS NOT NULL
					DROP TABLE ###local.tempTableName#_summary;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="qryGivingReport" access="public" output="false" returntype="struct">
		<cfargument name="strSQLPrep" type="struct" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="DRFrom" type="date" required="true">
		<cfargument name="DRTo" type="date" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct=structNew()>
		<cfset local.tempTableName = "rpt#getTickCount()#">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.returnStruct.qryData" result="local.returnStruct.qryDataResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				<cfif len(arguments.strSQLPrep.ruleSQL)>#PreserveSingleQuotes(arguments.strSQLPrep.ruleSQL)#</cfif>
					
				declare @orgID int, @RStartDate datetime, @REndDate datetime, @R1StartDate datetime, @R1EndDate datetime, 
					@R2StartDate datetime, @R2EndDate datetime, @tr_AllocSaleTrans int, @tr_AllocPayTrans int, @tr_DITSaleTrans int;
				set @orgID = #arguments.orgID#;
				set @RStartDate = '#arguments.DRFrom#';
				set @REndDate = '#arguments.DRTo# 23:59:59.997';
				set @R1StartDate = '#dateformat(dateAdd("yyyy",-1,arguments.DRFrom),"m/d/yyyy")#';
				set @R1EndDate = '#dateformat(dateAdd("yyyy",-1,arguments.DRTo),"m/d/yyyy")# 23:59:59.997';
				set @R2StartDate = '#dateformat(dateAdd("yyyy",-2,arguments.DRFrom),"m/d/yyyy")#';
				set @R2EndDate = '#dateformat(dateAdd("yyyy",-2,arguments.DRTo),"m/d/yyyy")# 23:59:59.997';
				set @tr_AllocSaleTrans = dbo.fn_tr_getRelationshipTypeID('AllocSaleTrans');
				set @tr_DITSaleTrans = dbo.fn_tr_getRelationshipTypeID('DITSaleTrans');
				set @tr_AllocPayTrans = dbo.fn_tr_getRelationshipTypeID('AllocPayTrans');

				declare @tblAllGLs TABLE (glOrder varchar(max), GLAccountID int, accountName varchar(max), accountCode varchar(200));
				insert into @tblAllGLs (glOrder, GLAccountID, accountName, accountCode)
				select gl.thePath, gl.GLAccountID, gl.thePathExpanded, gl.accountCode
				from dbo.fn_getRecursiveGLAccounts(@orgID) as gl
				where gl.AccountTypeID = 3
				and gl.status <> 'D'
				ORDER BY gl.thePath;

				insert into @tblAllGLs (glOrder, GLAccountID, accountName, accountCode)
				values ('xxxx', ********, 'Total Giving', '');
				insert into @tblAllGLs (glOrder, GLAccountID, accountName, accountCode)
				values ('xxxy', ********, '', '');

				IF OBJECT_ID('tempdb..##tmpRawData') IS NOT NULL
					DROP TABLE ##tmpRawData;

				create table ##tmpRawData (memberid int, company varchar(200), GLAccountID int, DRange char(2), col varchar(6), amt decimal(18,2));
				insert into ##tmpRawData (memberID, company, GlAccountID, DRange, col, amt)
				select memberid, company, GLAccountID, DRange, 'billed', sum(AmtBilled)
				from (
					select m.memberid, m.company, t.creditGLAccountID as GLAccountID,
						DRange = case 
								when t.transactionDate between @RStartDate and @REndDate then 'R' 
								when t.transactionDate between @R1StartDate and @R1EndDate then 'R1' 
								else 'R2' end,
						AmtBilled = t.amount
					from dbo.tr_transactions as t
					inner join dbo.tr_GLAccounts as gl on gl.orgID = @orgID and gl.GLAccountID = t.creditGLAccountID
					inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = t.assignedToMemberID
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID
					<cfif len(arguments.strSQLPrep.joinSQL)>#PreserveSingleQuotes(arguments.strSQLPrep.joinSQL)#</cfif>
					where t.ownedByOrgID = @orgID
					and t.statusID = 1
					and t.typeID in (1,7)
					and (t.transactionDate between @RStartDate and @REndDate OR t.transactionDate between @R1StartDate and @R1EndDate OR t.transactionDate between @R2StartDate and @R2EndDate)
						union all
					select m.memberid, m.company, t.creditGLAccountID as GLAccountID,
						DRange = case 
								when t.transactionDate between @RStartDate and @REndDate then 'R' 
								when t.transactionDate between @R1StartDate and @R1EndDate then 'R1' 
								else 'R2' end,
						AmtBilled = t.amount
					from dbo.tr_transactions as t
					inner join dbo.tr_GLAccounts as gl on gl.orgID = @orgID and gl.GLAccountID = t.creditGLAccountID and gl.status <> 'D'
					inner join dbo.tr_GLAccounts as gl2 on gl2.orgID = @orgID and gl2.GLAccountID = t.debitGLAccountID and gl2.GLCode = 'ACCOUNTSRECEIVABLE'
					inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = t.assignedToMemberID
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID
					<cfif len(arguments.strSQLPrep.joinSQL)>#PreserveSingleQuotes(arguments.strSQLPrep.joinSQL)#</cfif>
					where t.ownedByOrgID = @orgID
					and t.statusID = 1
					and t.typeID = 3
					and (t.transactionDate between @RStartDate and @REndDate OR t.transactionDate between @R1StartDate and @R1EndDate OR t.transactionDate between @R2StartDate and @R2EndDate)
						union all
					select m.memberid, m.company, t.debitGLAccountID as GLAccountID,
						DRange = case 
								when t.transactionDate between @RStartDate and @REndDate then 'R' 
								when t.transactionDate between @R1StartDate and @R1EndDate then 'R1' 
								else 'R2' end,
						AmtBilled = t.amount*-1
					from dbo.tr_transactions as t
					inner join dbo.tr_GLAccounts as gl on gl.orgID = @orgID and gl.GLAccountID = t.debitGLAccountID and gl.status <> 'D'
					inner join dbo.tr_GLAccounts as gl2 on gl2.orgID = @orgID and gl2.GLAccountID = t.creditGLAccountID and gl2.GLCode = 'ACCOUNTSRECEIVABLE'
					inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = t.assignedToMemberID
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID
					<cfif len(arguments.strSQLPrep.joinSQL)>#PreserveSingleQuotes(arguments.strSQLPrep.joinSQL)#</cfif>
					where t.ownedByOrgID = @orgID
					and t.statusID = 1
					and t.typeID = 3
					and (t.transactionDate between @RStartDate and @REndDate OR t.transactionDate between @R1StartDate and @R1EndDate OR t.transactionDate between @R2StartDate and @R2EndDate)
				) as innerSales
				group by memberid, company, GLAccountID, DRange;

				insert into ##tmpRawData (memberID, company, GlAccountID, DRange, col, amt)
				select memberid, company, GLAccountID, DRange, 'paid', sum(AmtPaid)
				from (
					select m.memberid, m.company, 
						case when saleAdjTaxDitT.typeID = 10 then saleAdjTaxDitT.debitGLAccountID else saleAdjTaxDitT.creditGLAccountID end as GLAccountID,
						DRange = case 
								when b.depositDate between @RStartDate and @REndDate then 'R' 
								when b.depositDate between @R1StartDate and @R1EndDate then 'R1' 
								else 'R2' end,
						AmtPaid = case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end 
					from dbo.tr_transactions as allocT
					inner join dbo.tr_relationships as allocR on allocR.orgID = @orgID and allocR.typeID = @tr_AllocSaleTrans and allocR.transactionID = allocT.transactionID
					inner join dbo.tr_transactions as saleAdjTaxDitT on saleAdjTaxDitT.ownedByOrgID = @orgID and saleAdjTaxDitT.transactionID = allocR.appliedToTransactionID and saleAdjTaxDitT.statusID = 1
					left outer join dbo.tr_relationships as ditR on ditR.orgID = @orgID and ditR.typeID = @tr_DITSaleTrans and ditR.transactionID = saleAdjTaxDitT.transactionID
					inner join dbo.tr_GLAccounts as gl on gl.orgID = @orgID and gl.GLAccountID = case when saleAdjTaxDitT.typeID = 10 then saleAdjTaxDitT.debitGLAccountID else saleAdjTaxDitT.creditGLAccountID end
					inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = saleAdjTaxDitT.assignedToMemberID
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID
					inner join dbo.tr_relationships as payR on payR.orgID = @orgID and payR.typeID = @tr_AllocPayTrans and payR.transactionID = allocT.transactionID
					inner join dbo.tr_transactions as payT on payT.ownedByOrgID = @orgID and payT.transactionID = payR.appliedToTransactionID and payT.statusID = 1
					inner join dbo.tr_GLAccounts as glAlloc on glAlloc.orgID = @orgID and glAlloc.GLAccountID = allocT.creditGLAccountID and glAlloc.status <> 'D'
					inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = allocT.transactionID
					inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
					<cfif len(arguments.strSQLPrep.joinSQL)>#PreserveSingleQuotes(arguments.strSQLPrep.joinSQL)#</cfif>
					where allocT.ownedByOrgID = @orgid
					and allocT.typeID = 5
					and allocT.statusID = 1
					and (b.depositDate between @RStartDate and @REndDate OR b.depositDate between @R1StartDate and @R1EndDate OR b.depositDate between @R2StartDate and @R2EndDate)
				) as innerAlloc
				group by memberid, company, GLAccountID, DRange;

				select 
					ROW_NUMBER() OVER (PARTITION BY pvtdata.memberid ORDER BY m.lastname, m.suffix, m.firstname, m.middlename, m.membernumber, gls.glOrder) as rowMember,
					ROW_NUMBER() OVER (ORDER BY m.lastname, m.suffix, m.firstname, m.middlename, m.membernumber, gls.glOrder) as reportOrder,
					pvtdata.memberid, pvtdata.memberid as reportMemberID,
					m.lastname + isnull(nullif(' ' + m.suffix,''),'') + ', ' + m.firstname + isnull(' ' + nullif(m.middlename,''),'') + ' (' + m.membernumber + ')' as mc_combinedName,
					m.company,
					gls.accountName, gls.accountcode, gls.GLAccountID, 
					isnull(round(pvtdata.billedR,0),0) as billedR, isnull(round(pvtdata.billedR1,0),0) as billedR1, isnull(round(pvtdata.billedR2,0),0) as billedR2,
					isnull(round(pvtdata.paidR,0),0) as paidR, isnull(round(pvtdata.paidR1,0),0) as paidR1, isnull(round(pvtdata.paidR2,0),0) as paidR2,
					PaidDiffR = case 
						when isnull(pvtdata.paidR1,0) <> 0 then ((isnull(pvtdata.paidR,0)-isnull(pvtdata.paidR1,0))/isnull(pvtdata.paidR1,0)) * 100
						when isnull(pvtdata.paidR1,0) = 0 and isnull(pvtdata.paidR,0) <> 0 then null
						else 0
						end,
					PaidDiffR1 = case 
						when isnull(pvtdata.paidR2,0) <> 0 then ((isnull(pvtdata.paidR1,0)-isnull(pvtdata.paidR2,0))/isnull(pvtdata.paidR2,0)) * 100
						when isnull(pvtdata.paidR2,0) = 0 and isnull(pvtdata.paidR1,0) <> 0 then null
						else 0
						end
				from (
					select memberid, GLAccountID, col+DRange as col, amt
					from ##tmpRawData
				) as tmp
				PIVOT (sum(amt) FOR col in ([billedR],[paidR],[billedR1],[paidR1],[billedR2],[paidR2])) as pvtdata
				left outer join dbo.ams_members as m on m.orgID = @orgID and m.memberid = pvtdata.memberid
				inner join @tblAllGLs as gls on gls.glAccountID = pvtdata.GLAccountID
				order by case when m.memberid is null then 0 else 1 end desc, 5, gls.glOrder;

				IF OBJECT_ID('tempdb..##tmpRawData') IS NOT NULL
					DROP TABLE ##tmpRawData;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="qryPaymentReport" access="public" output="false" returntype="struct">
		<cfargument name="strSQLPrep" type="struct" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="frmDRFrom" type="date" required="true">
		<cfargument name="frmDRTo" type="date" required="true">
		<cfargument name="frmLinkAllocType" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct=structNew()>
		<cfset local.tempTableName = "rpt#getTickCount()#">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.returnStruct.qryData" result="local.returnStruct.qryDataResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				<cfif len(arguments.strSQLPrep.ruleSQL)>#PreserveSingleQuotes(arguments.strSQLPrep.ruleSQL)#</cfif>
				
				declare @orgID int, @startdate datetime, @enddate datetime, @tr_AllocSaleTrans int, @tr_DITSaleTrans int;
				set @orgID = #arguments.orgID#;
				set @startdate = '#arguments.frmDRFrom#';
				set @enddate = '#arguments.frmDRTo# 23:59:59.997';
				set @tr_AllocSaleTrans = dbo.fn_tr_getRelationshipTypeID('AllocSaleTrans');
				set @tr_DITSaleTrans = dbo.fn_tr_getRelationshipTypeID('DITSaleTrans');
				
				declare @tblAllGLs TABLE (glOrder varchar(max), GLAccountID int, AccountTypeID int, accountName varchar(max), accountCode varchar(200));
				insert into @tblAllGLs (glOrder, GLAccountID, AccountTypeID, accountName, accountCode)
				SELECT gl.thePath, gl.GLAccountID, gl.AccountTypeID, gl.thePathExpanded, gl.accountCode
				FROM dbo.fn_getRecursiveGLAccounts(@orgID) as gl
				where gl.status <> 'D'
				ORDER BY gl.thePath;

				IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
					DROP TABLE ###local.tempTableName#;

				select memberid as reportMemberID, memberid, lastname, firstname, membernumber, company,
					sum(allocatedAmount) as allocatedAmount, GLAccountID, AccountName, AccountCode, GLOrder,
					firstname + ' ' + lastname + ' (' + membernumber + ')' as mc_combinedName
				into ###local.tempTableName#
				from (
					select m.memberid, m.lastname, m.firstname, m.membernumber, m.company, 
						case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocatedAmount,
						gl.GLAccountID, gl.AccountName, gl.AccountCode, gl.GLOrder
					from dbo.tr_transactions as allocT 
					inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = allocT.transactionID
					inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
					inner join dbo.tr_relationships as allocR on allocR.orgID = @orgID and allocR.typeID = @tr_AllocSaleTrans and allocR.transactionID = allocT.transactionID
					inner join dbo.tr_transactions as saleAdjTaxDitT on saleAdjTaxDitT.ownedByOrgID = @orgID and saleAdjTaxDitT.transactionID = allocR.appliedToTransactionID
					left outer join dbo.tr_relationships as ditR on ditR.orgID = @orgID and ditR.typeID = @tr_DITSaleTrans and ditR.transactionID = saleAdjTaxDitT.transactionID
					<cfif arguments.frmLinkAllocType eq "LinkAllocCash">
						inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = allocT.assignedToMemberID
					<cfelse>
						inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = saleAdjTaxDitT.assignedToMemberID
					</cfif>
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID
					inner join @tblAllGLs as gl on gl.glAccountID = case when saleAdjTaxDitT.typeID = 10 then saleAdjTaxDitT.debitGLAccountID else saleAdjTaxDitT.creditGLAccountID end and gl.AccountTypeID = 3
					inner join dbo.tr_GLAccounts as glAlloc on glAlloc.orgID = @orgID and glAlloc.GLAccountID = allocT.creditGLAccountID
					<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
					where allocT.ownedByOrgID = @orgID
					and allocT.typeID = 5
					and b.depositDate between @startdate and @enddate
						union all
					select m.memberid, m.lastname, m.firstname, m.membernumber, m.company, 
						case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocatedAmount,
						gl.GLAccountID, gl.AccountName, gl.AccountCode, gl.GLOrder
					from dbo.tr_transactions as VOT 
					inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = VOT.transactionID
					inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
					inner join dbo.tr_relationships as VOR on VOR.orgID = @orgID and VOR.transactionID = VOT.transactionID and VOR.typeID = 8
					inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = @orgID and allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
					inner join dbo.tr_relationships as allocR on allocR.orgID = @orgID and allocR.typeID = @tr_AllocSaleTrans and allocR.transactionID = allocT.transactionID
					inner join dbo.tr_transactions as saleAdjTaxDitT on saleAdjTaxDitT.ownedByOrgID = @orgID and saleAdjTaxDitT.transactionID = allocR.appliedToTransactionID
					left outer join dbo.tr_relationships as ditR on ditR.orgID = @orgID and ditR.typeID = @tr_DITSaleTrans and ditR.transactionID = saleAdjTaxDitT.transactionID
					<cfif arguments.frmLinkAllocType eq "LinkAllocCash">
						inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = allocT.assignedToMemberID
					<cfelse>
						inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = saleAdjTaxDitT.assignedToMemberID
					</cfif>
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID
					inner join @tblAllGLs as gl on gl.glAccountID = case when saleAdjTaxDitT.typeID = 10 then saleAdjTaxDitT.debitGLAccountID else saleAdjTaxDitT.creditGLAccountID end and gl.AccountTypeID = 3
					inner join dbo.tr_GLAccounts as glAlloc on glAlloc.orgID = @orgID and glAlloc.GLAccountID = allocT.creditGLAccountID
					<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
					where VOT.ownedByOrgID = @orgID
					and VOT.typeID = 8
					and b.depositDate between @startdate and @enddate					
				) as tmp
				group by memberid, lastname, firstname, membernumber, company,
					GLAccountID, AccountName, AccountCode, GLOrder;

				CREATE NONCLUSTERED INDEX IX_memberid ON ###local.tempTableName# (memberID);
				
				#makeTempTableAcceptNULLs("#local.tempTableName#")#

				select reportMemberID, accountName, allocatedAmount,
					ROW_NUMBER() OVER (ORDER BY case when memberID = ******** then 0 else 1 end desc, mc_combinedName, glOrder) as reportOrder
				from ###local.tempTableName#
				order by reportOrder;

				IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
					DROP TABLE ###local.tempTableName#;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>	

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="prepSQL" access="private" output="false" returntype="struct">
		<cfargument name="prepQry" type="query">
		
		<cfset var local = structNew()>
		<cfset local.arrJoins = arrayNew(1)>
		<cfset local.arrJoinsNoMemberData = arrayNew(1)>
		<cfset local.otherXML = XMLParse(arguments.prepQry.otherXML)>

		<!--- --------------- --->
		<!--- RUN MEMBER RULE --->
		<!--- --------------- --->
		<cfset local.prepMemberRule = prepSQL_memberrule(ruleID=arguments.prepQry.ruleID, orgID=arguments.prepQry.orgID)>
		<cfset local.ruleSQL = local.prepMemberRule.ruleSQL>
		<cfif len(local.prepMemberRule.arrJoins)>
			<cfset arrayAppend(local.arrJoins,local.prepMemberRule.arrJoins)>
		</cfif>
		<cfif len(local.prepMemberRule.arrJoinsNoMemberData)>
			<cfset arrayAppend(local.arrJoinsNoMemberData,local.prepMemberRule.arrJoinsNoMemberData)>
		</cfif>
		
		<!--- ------------------- --->
		<!--- RUN SUBSCRIBER RULE --->
		<!--- ------------------- --->
		<!--- if rule has no conditions, dont run rule. --->
		<cfif XMLSearch(local.otherXML,"count(/report/subrule//condition)") gt 0>
			<cfsavecontent variable="local.subRuleXSL">
				<cfoutput>
				<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
					<xsl:strip-space elements="subrule conditionset condition" />
					<xsl:output method="xml" encoding="UTF-8" cdata-section-elements="subrule" />
					<xsl:template match="extra" />
					<xsl:template match="subrule">
						<subrule><xsl:apply-templates/></subrule>
					</xsl:template>	
					<xsl:template match="conditionset">
						<xsl:if test="@act = 'exclude'"><xsl:text>NOT </xsl:text></xsl:if>
						<xsl:text>( </xsl:text><xsl:apply-templates/><xsl:text> )</xsl:text>
						<xsl:if test="position() != last()"><xsl:value-of select="concat(' ',../@op,' ')"/></xsl:if>
					</xsl:template>
					<xsl:template match="condition">
						<xsl:text>( </xsl:text>
						<xsl:text>( subType.typeID in (</xsl:text><xsl:value-of select="t"/><xsl:text>) )</xsl:text>
						<xsl:if test="string-length(s) &gt; 0">
							<xsl:text> AND ( sub.subscriptionID in (</xsl:text><xsl:value-of select="s"/><xsl:text>) )</xsl:text>
						</xsl:if>
						<xsl:if test="string-length(r) &gt; 0">
							<xsl:text> AND ( subrate.rateID in (</xsl:text><xsl:value-of select="r"/><xsl:text>) )</xsl:text>
						</xsl:if>
						<xsl:if test="string-length(ss) &gt; 0">
							<xsl:text> AND ( substatus.statusID in (</xsl:text><xsl:value-of select="ss"/><xsl:text>) )</xsl:text>
						</xsl:if>
						<xsl:if test="string-length(ps) &gt; 0">
							<xsl:text> AND ( subpaystatus.statusID in (</xsl:text><xsl:value-of select="ps"/><xsl:text>) )</xsl:text>
						</xsl:if>
						<xsl:if test="string-length(pm) &gt; 0">
							<xsl:text> AND ( 1 = CASE WHEN ('</xsl:text><xsl:value-of select="pm"/><xsl:text>' = 'Y' AND s.payProfileID is not null) OR ('</xsl:text><xsl:value-of select="pm"/><xsl:text>' = 'N' AND s.payProfileID is null) THEN 1 ELSE 0 END )</xsl:text>
						</xsl:if>
						<xsl:choose>
							<xsl:when test="string-length(d/s0) &gt; 0">
								<xsl:text> AND ( s.subStartDate &gt;= '</xsl:text><xsl:value-of select="d/s0"/><xsl:text>' )</xsl:text>
							</xsl:when>
							<xsl:when test="string-length(d/s1) &gt; 0">
								<xsl:text> AND ( s.subStartDate between '</xsl:text><xsl:value-of select="d/s1"/><xsl:text>' and '</xsl:text><xsl:value-of select="d/s2"/><xsl:text>' )</xsl:text>
							</xsl:when>
						</xsl:choose>
						<xsl:choose>
							<xsl:when test="string-length(d/e0) &gt; 0">
								<xsl:text> AND ( s.subEndDate &lt;= '</xsl:text><xsl:value-of select="d/e0"/><xsl:text> 23:59:59.997' )</xsl:text>
							</xsl:when>
							<xsl:when test="string-length(d/e1) &gt; 0">
								<xsl:text> AND ( s.subEndDate between '</xsl:text><xsl:value-of select="d/e1"/><xsl:text>' and '</xsl:text><xsl:value-of select="d/e2"/><xsl:text> 23:59:59.997' )</xsl:text>
							</xsl:when>
						</xsl:choose>
						<xsl:text> )</xsl:text>
						<xsl:if test="position() != last()"><xsl:value-of select="concat(' ',../@op,' ')"/></xsl:if>
					</xsl:template>
					<xsl:template match="/">
						<xsl:apply-templates />
					</xsl:template>
				</xsl:stylesheet>
				</cfoutput>
			</cfsavecontent>

			<cfset local.subscriberRuleXML = XMLTransform(local.otherXML.report.subrule,local.subRuleXSL)>
			<cfset local.subscriberRuleSQL = XMLSearch(local.subscriberRuleXML,"string(subrule)")>

			<cfsavecontent variable="local.ruleSQL">
				<cfoutput>
				#local.ruleSQL#

				declare @tblS TABLE (subscriberID int PRIMARY KEY);
		
				insert into @tblS (subscriberID)
				select s.subscriberID
				from dbo.sub_subscribers as s
				inner join dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
				inner join dbo.sub_types as subType on subType.typeID = sub.typeID
				inner join dbo.sub_statuses as substatus on substatus.statusID = s.statusID
				inner join dbo.sub_paymentstatuses as subpaystatus on subpaystatus.statusID = s.paymentStatusID
				inner join dbo.sub_rateFrequencies subrf on subrf.RFID = s.RFID
				inner join dbo.sub_rates subrate on subrate.rateID = subrf.rateID
				where subType.siteID = #val(arguments.prepQry.siteID)#
				and #local.subscriberRuleSQL#;
				</cfoutput>
			</cfsavecontent>
			<cfset arrayAppend(local.arrJoins,"inner join @tblS as tblS on tblS.subscriberID = s.subscriberID")>
			<cfset arrayAppend(local.arrJoinsNoMemberData,"inner join @tblS as tblS on tblS.subscriberID = s.subscriberID")>
		</cfif>
		
		<!--- ----------------- --->
		<!--- RUN EVENTS FILTER --->
		<!--- ----------------- --->
		<cfif ListLen(XMLSearch(local.otherXML,"string(/report/extra/eidlist/text())"))>
			<cfsavecontent variable="local.ruleSQL">
				<cfoutput>
				#local.ruleSQL#

				declare @tblE TABLE (eventID int PRIMARY KEY);
		
				-- get events on site
				IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
					DROP TABLE ##tmpEventsOnSite;
				CREATE TABLE ##tmpEventsOnSite (calendarID int, eventID int, [status] char(1), isPastEvent bit,
					startTime datetime, endTime datetime, timeZoneID int, timeZoneCode varchar(25), timeZoneAbbr varchar(4),
					displayStartTime datetime, displayEndTime datetime, displayTimeZoneID int, displayTimeZoneCode varchar(25),
					displayTimeZoneAbbr varchar(4), siteResourceID int, isAllDayEvent bit, 
					altRegistrationURL varchar(300), eventTitle varchar(200), eventSubTitle varchar(200), locationTitle varchar(200), 
					categoryIDList varchar(max));
				EXEC dbo.ev_getEventsOnSite @siteID=#arguments.prepQry.siteid#, @startDate=null, @endDate=null, @categoryIDList='';

				insert into @tblE (eventID)
				SELECT distinct tmp.eventid
				FROM ##tmpEventsOnSite as tmp
				inner join dbo.fn_intListToTable('0#XMLSearch(local.otherXML,"string(/report/extra/eidlist/text())")#',',') dg on dg.listitem = tmp.eventID;

				<!--- dont drop the temp table here because we need it in reports --->
				</cfoutput>
			</cfsavecontent>
			<cfset arrayAppend(local.arrJoins,"inner join @tblE as tblE on tblE.eventID = e.eventID")>
			<cfset arrayAppend(local.arrJoinsNoMemberData,"inner join @tblE as tblE on tblE.eventID = e.eventID")>
		</cfif>
		
		<!--- ------------- --->
		<!--- RUN GL FILTER --->
		<!--- ------------- --->
		<cfif ListLen(XMLSearch(local.otherXML,"string(/report/extra/gllist/text())"))>
			<cfsavecontent variable="local.ruleSQL">
				<cfoutput>
				#local.ruleSQL#

				declare @tblGL TABLE (GLAccountID int PRIMARY KEY);
		
				insert into @tblGL (GLAccountID)
				SELECT gl.GLAccountID
				FROM dbo.tr_GLAccounts as gl
				inner join dbo.fn_intListToTable('0#XMLSearch(local.otherXML,"string(/report/extra/gllist/text())")#',',') dg on dg.listitem = gl.GLAccountID
				where gl.orgID = #arguments.prepQry.orgID#
				and gl.AccountTypeID = 3
				and gl.status <> 'D';
				</cfoutput>
			</cfsavecontent>
			<cfset arrayAppend(local.arrJoins,"inner join @tblGL as tblGL on tblGL.GLAccountID = gl.GLAccountID")>
			<cfset arrayAppend(local.arrJoinsNoMemberData,"inner join @tblGL as tblGL on tblGL.GLAccountID = gl.GLAccountID")>
		</cfif>

		<!--- ------------------------- --->
		<!--- RUN REFERRAL PANEL FILTER --->
		<!--- ------------------------- --->
		<cfif ListLen(XMLSearch(local.otherXML,"string(/report/extra/rplist/text())"))>
			<cfsavecontent variable="local.ruleSQL">
				<cfoutput>
				#local.ruleSQL#

				declare @tblRP TABLE (panelID int PRIMARY KEY, panelParentID int);
						
				insert into @tblRP (panelID, panelParentID)
				SELECT p.panelID, panelParentID
				FROM dbo.ref_panels as p
				inner join dbo.fn_intListToTable('0#XMLSearch(local.otherXML,"string(/report/extra/rplist/text())")#',',') dg on dg.listitem = p.panelID
				inner join dbo.ref_referrals as r on r.referralID = p.referralID
				inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = r.applicationInstanceID
					and ai.siteID = #arguments.prepQry.siteID#
				inner join dbo.ref_panelStatus as ps on ps.panelStatusID = p.statusID
					and ps.statusName in ('Inactive','Active');
				</cfoutput>
			</cfsavecontent>
			<cfset arrayAppend(local.arrJoins,"inner join @tblRP as tblRP on tblRP.panelID = p.panelID")>
			<cfset arrayAppend(local.arrJoinsNoMemberData,"inner join @tblRP as tblRP on tblRP.panelID = p.panelID")>
		</cfif>
		
		<!--- return variables for use in report --->
		<cfset local.sql = {
			joinSQL=arrayToList(local.arrJoins,' '),
			ruleSQL=local.ruleSQL,
			joinSQLNoMemberData=arrayToList(local.arrJoinsNoMemberData,' ')
		}>

		<cfreturn local.sql>
	</cffunction>	

	<cffunction name="prepSQL_memberrule" access="private" output="false" returntype="struct">
		<cfargument name="ruleID" type="numeric" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.ruleErr = false>
		<cfset local.ruleSQL = "">
		<cfset local.arrJoins = "">
		<cfset local.arrJoinsNoMemberData = "">

		<!--- if rule has no conditions, dont run rule. --->
		<cfset local.numConditions = countRuleConditions(ruleID=arguments.ruleID, orgID=arguments.orgID)>
		<cfif local.numConditions gt 0>
			<!--- ensure rule is active. show error if necessary --->
			<cfset local.msg = activateRule(ruleID=arguments.ruleID, forceCache=true)>
			<cfif local.msg is not 0>
				<cfset local.ruleErr = true>
			<cfelse>
				<cfsavecontent variable="local.ruleSQL">
					<cfoutput>
					#local.ruleSQL#
					
					IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
						DROP TABLE ##tmpVGRMembers;
					CREATE TABLE ##tmpVGRMembers (memberID int PRIMARY KEY);

					EXEC dbo.ams_RunVirtualGroupRuleV2 @orgID=#arguments.orgID#, @ruleID=#arguments.ruleID#;
					</cfoutput>
				</cfsavecontent>
				
				<!--- If you change this, search in reports for places that alter it --->
				<cfset local.arrJoins = "inner join ##tmpVGRMembers as tblM on tblM.memberID = m.memberID">
				<cfset local.arrJoinsNoMemberData = "inner join ##tmpVGRMembers as tblM on tblM.memberID = m.memberID">
			</cfif>
		</cfif>
		
		<cfset local.returnStruct = { 
				ruleErr = local.ruleErr,
				ruleSQL = local.ruleSQL,
				arrJoins = local.arrJoins,
				arrJoinsNoMemberData = local.arrJoinsNoMemberData
			}>

		<cfreturn local.returnStruct>
	</cffunction>	

	<cffunction name="countRuleConditions" access="private" output="false" returntype="numeric">
		<cfargument name="ruleID" type="numeric" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">

		<cfset var qryRuleConditions = "">

		<cfquery name="qryRuleConditions" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">,
				@ruleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">;
			
			select vgrv.conditionCount
			from dbo.ams_virtualGroupRules as vgr
			inner join dbo.ams_virtualGroupRuleVersions as vgrv on vgrv.orgID = @orgID
				and vgrv.ruleVersionID = vgr.activeVersionID
			where vgr.ruleID = @ruleID
			and vgr.orgID = @orgID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn val(qryRuleConditions.conditionCount)>
	</cffunction>	

	<cffunction name="activateRule" access="public" output="false" returntype="numeric">
		<cfargument name="ruleID" type="numeric" required="yes">
		<cfargument name="forceCache" type="boolean" required="no" default="0"> <!--- used by reports to force condition cache rebuild since they would be skipped otherwise --->
		
		<cfset var local = structNew()>
		<cfset local.msg = 0>

		<cftry>
			<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="ams_activateVirtualGroupRule">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.forceCache#">
			</cfstoredproc>
		<cfcatch type="Any">
			<cfset local.msg = 2>
		</cfcatch>
		</cftry>

		<cfreturn local.msg>
	</cffunction>		

	<cffunction name="prepSQL_fieldset" access="private" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="existingFields" type="string" required="yes">
		<cfargument name="existingAliases" type="string" required="yes">
		<cfargument name="fieldSetArray" type="array" required="yes">
		<cfargument name="fieldSetNameFormat" type="string" required="yes">
		<cfargument name="splitMultiValueDelim" type="string" required="yes">
		<cfargument name="combineAddressDelim" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.arrOnlyMemberDataFieldNames = arrayNew(1)>

		<!--- ------------------- --->
		<!--- GET EXISTING FIELDS --->
		<!--- ------------------- --->
		<cfset arguments.existingFields = Replace(arguments.existingFields," ","","ALL")>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryExistingFields" result="local.qryExistingFieldsResult">
			select reverse(parsename(replace(reverse(fieldcode),'_','.'),1)) as fieldcodeSect, fieldcode, dbobject, dbobjectAlias, dbField, fieldLabel
			from dbo.fn_ams_getPossibleMemberFields(dbo.fn_getOrgIDFromSiteID(#arguments.siteID#))
			where fieldCode in (<cfqueryparam cfsqltype="cf_sql_varchar" list="true" value="#arguments.existingFields#">)
		</cfquery>

		<!--- ---------------------- --->
		<!--- GET FIELDS IN FIELDSET --->
		<!--- ---------------------- --->
		<cfset local.qryFSFields = QueryNew("fieldOrder,fieldcodeSect,allowMultiple,fieldcode,dataTypeCode,dbobject,dbobjectAlias,dbField,fieldLabel,fieldSetName,isGrouped","integer,varchar,bit,varchar,varchar,varchar,varchar,varchar,varchar,varchar,bit")>
		<cfset local.memberFieldsets = CreateObject("component","models.system.platform.memberFieldsets")>
		<cfset local.strFC = structNew()>
		<cfset local.fieldOrder = 0>
		<cfloop array="#arguments.fieldSetArray#" index="local.thisFS">
			<cfset local.xmlFields = local.memberFieldsets.getMemberFieldsXMLByUID(uid=local.thisFS, usage="reportResults")>
			<cfloop array="#local.xmlFields.xmlRoot.xmlChildren#" index="local.thisField">
				<cfif NOT StructKeyExists(local.strFC,local.thisField.xmlattributes.fieldCode)>
					<cfset local.fieldOrder = local.fieldOrder + 1>
					<cfset QueryAddRow(local.qryFSFields)>
					<cfset QuerySetCell(local.qryFSFields,"fieldOrder",local.fieldOrder)>
					<cfset QuerySetCell(local.qryFSFields,"fieldcodeSect",getToken(local.thisField.xmlattributes.fieldCode,1,"_"))>
					<cfset QuerySetCell(local.qryFSFields,"allowMultiple",local.thisField.xmlattributes.allowMultiple)>
					<cfset QuerySetCell(local.qryFSFields,"fieldcode",local.thisField.xmlattributes.fieldCode)>
					<cfset QuerySetCell(local.qryFSFields,"dataTypeCode",local.thisField.xmlattributes.dataTypeCode)>
					<cfset QuerySetCell(local.qryFSFields,"dbObject",local.thisField.xmlattributes.dbObject)>
					<cfset QuerySetCell(local.qryFSFields,"dbField",local.thisField.xmlattributes.dbField)>
					<!--- remove ? in field labels because of https://luceeserver.atlassian.net/browse/LDEV-4424 --->
					<cfset QuerySetCell(local.qryFSFields,"fieldLabel",Replace(local.thisField.xmlattributes.fieldLabel,"?","","ALL"))>
					<cfset QuerySetCell(local.qryFSFields,"dbObjectAlias",local.thisField.xmlattributes.dbObjectAlias)>
					<cfset QuerySetCell(local.qryFSFields,"fieldSetName",local.xmlFields.xmlRoot.xmlAttributes.fieldsetName)>
					<cfset QuerySetCell(local.qryFSFields,"isGrouped",local.thisField.xmlattributes.isGrouped)>
					<cfset StructInsert(local.strFC,local.thisField.xmlattributes.fieldCode,"")>
				</cfif>
			</cfloop>
		</cfloop>
		
		<!--- ------------------------------------------------------------------ --->
		<!--- OUTPUT FIELDS: FIELDS IN FIELDSETS THAT ARE NOT IN EXISTING FIELDS --->
		<!--- ------------------------------------------------------------------ --->
		<cfquery name="local.qryOutputFieldsGrouped" dbtype="query">
			select *
			from [local].qryFSFields
			where isGrouped = 1
			<cfif local.qryExistingFields.recordcount gt 0>
				and fieldCode NOT IN (#QuotedValueList(local.qryExistingFields.fieldcode)#)
			</cfif>
			order by fieldOrder
		</cfquery>		
		<cfquery name="local.qryOutputFields" dbtype="query">
			select *
			from [local].qryFSFields
			where isGrouped = 0
			<cfif local.qryExistingFields.recordcount gt 0>
				and fieldCode NOT IN (#QuotedValueList(local.qryExistingFields.fieldcode)#)
			</cfif>
			order by fieldOrder
		</cfquery>		
		
		<!--- ----------- --->
		<!--- SELECT LIST --->
		<!--- ----------- --->
		<cfset local.arrSelect = arrayNew(1)>

		<cfif local.qryOutputFieldsGrouped.recordcount>
			<cfoutput query="local.qryOutputFieldsGrouped" group="fieldSetName">
				<cfsavecontent variable="local.tmpSelectGrouped">
					[#local.qryOutputFieldsGrouped.fieldSetName#] = dbo.fn_varCharListNoEmpty(''
						<cfoutput>
							<cfif listFindNoCase("vwma,vwme,vwmw,vwmpl,vwmd",local.qryOutputFieldsGrouped.dbobjectAlias)>
								<cfset arrayAppend(local.arrOnlyMemberDataFieldNames, local.qryOutputFieldsGrouped.dbField)>
								<cfset local.thisDBObjectAlias = "mockVWMD">
							<cfelse>
								<cfset local.thisDBObjectAlias = local.qryOutputFieldsGrouped.dbobjectAlias>
							</cfif>
							<cfif NOT listFindNoCase("ml,grps,grpsLink,acct",local.thisDBObjectAlias)>
								<cfif not listFindNoCase("m_membertypeid,m_status,m_recordtypeid", local.qryOutputFieldsGrouped.fieldCode)>
									+ isnull(cast(#local.thisDBObjectAlias#.[#local.qryOutputFieldsGrouped.dbField#] as varchar(max)),'')
									+ char(7)
								<cfelse>
									<cfswitch expression="#local.qryOutputFieldsGrouped.fieldCode#">
										<cfcase value="m_membertypeid">
											+ case when #local.thisDBObjectAlias#.[#local.qryOutputFieldsGrouped.dbField#] = 1 then 'Guest' when #local.thisDBObjectAlias#.[#local.qryOutputFieldsGrouped.dbField#] = 2 then 'User' else '' end 
											+ char(7)
										</cfcase>
										<cfcase value="m_status">
											+ case when #local.thisDBObjectAlias#.[#local.qryOutputFieldsGrouped.dbField#] = 'A' then 'Active' else 'Inactive' end 
											+ char(7)
										</cfcase>
										<cfcase value="m_recordtypeid">
											+ isnull(mrt.recordTypeName,'')
											+ char(7)
										</cfcase>
									</cfswitch>
								</cfif>
							<cfelseif local.qryOutputFieldsGrouped.dbField eq "ml_datelastlogin_0">
								+ isnull(convert(varchar(10),(select max(dateLastLogin) from dbo.ams_memberNetworkProfiles where memberID = m.memberID and status = 'A'),101),'')
								+ char(7)
							<cfelseif left(local.qryOutputFieldsGrouped.dbField,17) eq "ml_datelastlogin_">
								+ isnull(convert(varchar(10),(select max(dateLastLogin) from dbo.ams_memberNetworkProfiles where memberID = m.memberID and siteID = #val(GetToken(local.qryOutputFieldsGrouped.fieldcode,3,'_'))# and status = 'A'),101),'')
								+ char(7)
							<cfelseif local.qryOutputFieldsGrouped.dbField eq "acct_balance_0">
								+ cast(isNull((select sum(balance) from dbo.tr_creditBalances where memberID = m.memberID),0) as varchar(20)) 
								+ char(7)
							<cfelseif left(local.qryOutputFieldsGrouped.dbField,13) eq "acct_balance_">
								+ cast(isNull((select sum(balance) from dbo.tr_creditBalances where memberID = m.memberID and profileID = #val(GetToken(local.qryOutputFieldsGrouped.fieldcode,3,'_'))#),0) as varchar(20)) 
								+ char(7)
							<cfelseif listFindNoCase("grps,grpsLink",local.thisDBObjectAlias)>
								+ case when exists (select memberid from dbo.cache_members_groups where memberID = m.memberID and groupID = #val(GetToken(local.qryOutputFieldsGrouped.fieldcode,2,'_'))#) then '#replace(local.qryOutputFieldsGrouped.fieldLabel,"'","''","ALL")#' else '' end 
								+ char(7)
							</cfif>
						</cfoutput>
					,char(7),'|')
				</cfsavecontent>
				<cfset arrayAppend(local.arrSelect,local.tmpSelectGrouped)>
			</cfoutput>
		</cfif>

		<cfloop query="local.qryOutputFields">
			<cfif listFindNoCase("vwma,vwme,vwmw,vwmpl,vwmd",local.qryOutputFields.dbobjectAlias)>
				<cfset arrayAppend(local.arrOnlyMemberDataFieldNames, local.qryOutputFields.dbField)>
				<cfset local.thisDBObjectAlias = "mockVWMD">
			<cfelse>
				<cfset local.thisDBObjectAlias = local.qryOutputFields.dbobjectAlias>
			</cfif>

			<cfif NOT listFindNoCase("ml,grps,grpsLink,acct",local.thisDBObjectAlias)>
				<cfif not listFindNoCase("m_membertypeid,m_status,m_recordtypeid", local.qryOutputFields.fieldCode)>
					<cfset arrayAppend(local.arrSelect,"#local.thisDBObjectAlias#.[#local.qryOutputFields.dbField#] as [#local.qryOutputFields.fieldLabel#]")>
				<cfelse>
					<cfswitch expression="#local.qryOutputFields.fieldCode#">
						<cfcase value="m_membertypeid">
							<cfset arrayAppend(local.arrSelect,"(case when #local.thisDBObjectAlias#.[#local.qryOutputFields.dbField#] = 1 then 'Guest' when #local.thisDBObjectAlias#.[#local.qryOutputFields.dbField#] = 2 then 'User' else '' end) as [#local.qryOutputFields.fieldLabel#]")>
						</cfcase>
						<cfcase value="m_status">
							<cfset arrayAppend(local.arrSelect,"(case when #local.thisDBObjectAlias#.[#local.qryOutputFields.dbField#] = 'A' then 'Active' else 'Inactive' end) as [#local.qryOutputFields.fieldLabel#]")>
						</cfcase>
						<cfcase value="m_recordtypeid">
							<cfset arrayAppend(local.arrSelect,"mrt.recordTypeName as [#local.qryOutputFields.fieldLabel#]")>
						</cfcase>
						<cfdefaultcase>
							<cfset arrayAppend(local.arrSelect,"#local.thisDBObjectAlias#.[#local.qryOutputFields.dbField#] as [#local.qryOutputFields.fieldLabel#]")>
						</cfdefaultcase>
					</cfswitch>
				</cfif>
			<cfelseif local.qryOutputFields.dbField eq "ml_datelastlogin_0">
				<cfset arrayAppend(local.arrSelect,"(select max(dateLastLogin) from dbo.ams_memberNetworkProfiles where memberID = m.memberID and status = 'A') as [#local.qryOutputFields.fieldLabel#]")>
			<cfelseif left(local.qryOutputFields.dbField,17) eq "ml_datelastlogin_">
				<cfset arrayAppend(local.arrSelect,"(select max(dateLastLogin) from dbo.ams_memberNetworkProfiles where memberID = m.memberID and siteID = #val(GetToken(local.qryOutputFields.fieldcode,3,'_'))# and status = 'A') as [#local.qryOutputFields.fieldLabel#]")>
			<cfelseif local.qryOutputFields.dbField eq "acct_balance_0">
				<cfset arrayAppend(local.arrSelect,"isNull((select sum(balance) from dbo.tr_creditBalances where memberID = m.memberID),0) as [#local.qryOutputFields.fieldLabel#]")>
			<cfelseif left(local.qryOutputFields.dbField,13) eq "acct_balance_">
				<cfset arrayAppend(local.arrSelect,"isNull((select sum(balance) from dbo.tr_creditBalances where memberID = m.memberID and profileID = #val(GetToken(local.qryOutputFields.fieldcode,3,'_'))#),0) as [#local.qryOutputFields.fieldLabel#]")>
			<cfelseif listFindNoCase("grps,grpsLink",local.thisDBObjectAlias)>
				<cfset arrayAppend(local.arrSelect,"case when exists (select memberid from dbo.cache_members_groups where memberID = m.memberID and groupID = #val(GetToken(local.qryOutputFields.fieldcode,2,'_'))#) then '#replace(local.qryOutputFields.fieldLabel,'''','''''','ALL')#' else null end as [#local.qryOutputFields.fieldLabel#]")>
			</cfif>
		</cfloop>

		<!--- if we have any grouped fields, put them back into the query sent back --->
		<cfif local.qryOutputFieldsGrouped.recordcount>
			<cfoutput query="local.qryOutputFieldsGrouped" group="fieldSetName">
				<cfset QueryAddRow(local.qryOutputFields)>
				<cfset QuerySetCell(local.qryOutputFields,"fieldOrder",local.qryOutputFieldsGrouped.fieldOrder)>
				<cfset QuerySetCell(local.qryOutputFields,"fieldcodeSect",'mfs')>
				<cfset QuerySetCell(local.qryOutputFields,"allowMultiple",1)>
				<cfset QuerySetCell(local.qryOutputFields,"fieldcode",'mfs_#local.qryOutputFieldsGrouped.currentrow#')>
				<cfset QuerySetCell(local.qryOutputFields,"dataTypeCode",'STRING')>
				<cfset QuerySetCell(local.qryOutputFields,"dbObject",'generated')>
				<cfset QuerySetCell(local.qryOutputFields,"dbField",'mfs_#local.qryOutputFieldsGrouped.currentrow#')>
				<cfset QuerySetCell(local.qryOutputFields,"isGrouped",1)>
				<!--- remove ? in field labels because of https://luceeserver.atlassian.net/browse/LDEV-4424 --->
				<cfset QuerySetCell(local.qryOutputFields,"fieldLabel",replace(local.qryOutputFieldsGrouped.fieldSetName,"?","","ALL"))>
				<cfset QuerySetCell(local.qryOutputFields,"fieldSetName",local.qryOutputFieldsGrouped.fieldSetName)>
				<cfset QuerySetCell(local.qryOutputFields,"dbObjectAlias",'mfs')>
			</cfoutput>
		</cfif>

		<!--- add mc_combinedname to select list if there are any name fields --->
		<!--- also add mc_combinednameNoMemNum with no member number --->
		<cfquery name="local.qryGetAllNames" dbtype="query">
			select fieldcode
			from [local].qryFSFields
			where fieldcodeSect = 'm'
				union
			select fieldcode
			from [local].qryExistingFields
			where fieldcodeSect = 'm'
		</cfquery>
		<cfquery name="local.qryGetNames" dbtype="query">
			select distinct fieldcode from [local].qryGetAllNames
		</cfquery>
		<cfif local.qryGetNames.recordcount>
			<cfset local.lstNameFields = valueList(local.qryGetNames.fieldcode)>
			<cfsavecontent variable="local.tmpSel">
				<cfoutput>
				<cfswitch expression="#arguments.fieldSetNameFormat#">
					<cfcase value="LSXPFM">
						''
						<cfif listFindNoCase(local.lstNameFields,'m_lastname')>+ m.lastname + ', '</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_suffix')>+ isnull(nullif(m.suffix,'') + ', ','')</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_professionalsuffix')>+ isnull(nullif(m.professionalsuffix,'') + ', ','')</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_prefix')>+ isnull(' ' + nullif(m.prefix,''),'')</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_firstname')>+ ' ' + m.firstname</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_middlename')>+ isnull(' ' + nullif(m.middlename,''),'')</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_membernumber')>+ ' (' + m.membernumber + ')'</cfif>
					</cfcase>
					<cfdefaultcase>
						''
						<cfif listFindNoCase(local.lstNameFields,'m_prefix')>+ isnull(nullif(m.prefix,'') + ' ','')</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_firstname')>+ m.firstname</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_middlename')>+ isnull(' ' + nullif(m.middlename,''),'')</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_lastname')>+ ' ' + m.lastname</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_suffix')>+ isnull(', ' + nullif(m.suffix,''),'')</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_professionalsuffix')>+ isnull(', ' + nullif(m.professionalsuffix,''),'')</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_membernumber')>+ ' (' + m.membernumber + ')'</cfif>
					</cfdefaultcase>
				</cfswitch>
				as mc_combinedName
				</cfoutput>
			</cfsavecontent>
			<cfset arrayAppend(local.arrSelect,local.tmpSel)>

			<cfset QueryAddRow(local.qryOutputFields)>
			<cfset QuerySetCell(local.qryOutputFields,"fieldcodeSect","mc")>
			<cfset QuerySetCell(local.qryOutputFields,"dbObject","generated")>
			<cfset QuerySetCell(local.qryOutputFields,"fieldcode","mc_combinedName")>
			<cfset QuerySetCell(local.qryOutputFields,"dbField","mc_combinedName")>
			<cfset QuerySetCell(local.qryOutputFields,"fieldLabel","Extended MemberNumber")>
			<cfset QuerySetCell(local.qryOutputFields,"dbObjectAlias","mc")>

			<cfsavecontent variable="local.tmpSel">
				<cfoutput>
				<cfswitch expression="#arguments.fieldSetNameFormat#">
					<cfcase value="LSXPFM">
						''
						<cfif listFindNoCase(local.lstNameFields,'m_lastname')>+ m.lastname + ', '</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_suffix')>+ isnull(nullif(m.suffix,'') + ', ','')</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_professionalsuffix')>+ isnull(nullif(m.professionalsuffix,'') + ', ','')</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_prefix')>+ isnull(' ' + nullif(m.prefix,''),'')</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_firstname')>+ ' ' + m.firstname</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_middlename')>+ isnull(' ' + nullif(m.middlename,''),'')</cfif>
					</cfcase>
					<cfdefaultcase>
						''
						<cfif listFindNoCase(local.lstNameFields,'m_prefix')>+ isnull(nullif(m.prefix,'') + ' ','')</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_firstname')>+ m.firstname</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_middlename')>+ isnull(' ' + nullif(m.middlename,''),'')</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_lastname')>+ ' ' + m.lastname</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_suffix')>+ isnull(', ' + nullif(m.suffix,''),'')</cfif>
						<cfif listFindNoCase(local.lstNameFields,'m_professionalsuffix')>+ isnull(', ' + nullif(m.professionalsuffix,''),'')</cfif>
					</cfdefaultcase>
				</cfswitch>
				as mc_combinedNameNoMemNum
				</cfoutput>
			</cfsavecontent>
			<cfset arrayAppend(local.arrSelect,local.tmpSel)>

			<cfset QueryAddRow(local.qryOutputFields)>
			<cfset QuerySetCell(local.qryOutputFields,"fieldcodeSect","mc")>
			<cfset QuerySetCell(local.qryOutputFields,"dbObject","generated")>
			<cfset QuerySetCell(local.qryOutputFields,"fieldcode","mc_combinedNameNoMemNum")>
			<cfset QuerySetCell(local.qryOutputFields,"dbField","mc_combinedNameNoMemNum")>
			<cfset QuerySetCell(local.qryOutputFields,"fieldLabel","Extended Name")>
			<cfset QuerySetCell(local.qryOutputFields,"dbObjectAlias","mc")>
		</cfif>

		<!--- add mc_combinedaddress to select list if there are any address fields --->
		<cfif len(arguments.combineAddressDelim)>
			<cfquery name="local.qryGetAllAddresses" dbtype="query">
				select fieldcode, dbField, fieldcodeSect
				from [local].qryFSFields
				where fieldcodeSect in ('ma','mat')
					union
				select fieldcode, dbField, fieldcodeSect
				from [local].qryExistingFields
				where fieldcodeSect in ('ma','mat')
			</cfquery>
			<cfquery name="local.qryGetAddresses" dbtype="query">
				select distinct fieldcode, dbField, fieldcodeSect
				from [local].qryGetAllAddresses
			</cfquery>
			<cfif local.qryGetAddresses.recordcount>
				<cfset local.strAddr = structNew()>
				<cfloop query="local.qryGetAddresses">
					<cfset local.ATID = getToken(local.qryGetAddresses.fieldcode,2,"_")>
					<cfif local.qryGetAddresses.fieldcodeSect eq "mat">
						<cfset local.ATID = "t" & local.ATID>
					</cfif>
					<cfif NOT structKeyExists(local.strAddr,local.ATID)>
						<cfset local.strAddr[local.ATID] = arrayNew(1)>
						<cfset ArraySet(local.strAddr[local.ATID],1,9,"")>
					</cfif>
					<cfif right(local.qryGetAddresses.fieldcode,5) eq "_attn"><cfset local.strAddr[local.ATID][1] = local.qryGetAddresses.dbField></cfif>
					<cfif right(local.qryGetAddresses.fieldcode,9) eq "_address1"><cfset local.strAddr[local.ATID][2] = local.qryGetAddresses.dbField></cfif>
					<cfif right(local.qryGetAddresses.fieldcode,9) eq "_address2"><cfset local.strAddr[local.ATID][3] = local.qryGetAddresses.dbField></cfif>
					<cfif right(local.qryGetAddresses.fieldcode,9) eq "_address3"><cfset local.strAddr[local.ATID][4] = local.qryGetAddresses.dbField></cfif>
					<cfif right(local.qryGetAddresses.fieldcode,5) eq "_city"><cfset local.strAddr[local.ATID][5] = local.qryGetAddresses.dbField></cfif>
					<cfif right(local.qryGetAddresses.fieldcode,10) eq "_stateprov"><cfset local.strAddr[local.ATID][6] = local.qryGetAddresses.dbField></cfif>
					<cfif right(local.qryGetAddresses.fieldcode,11) eq "_postalcode"><cfset local.strAddr[local.ATID][7] = local.qryGetAddresses.dbField></cfif>
					<cfif right(local.qryGetAddresses.fieldcode,7) eq "_county"><cfset local.strAddr[local.ATID][8] = local.qryGetAddresses.dbField></cfif>
					<cfif right(local.qryGetAddresses.fieldcode,8) eq "_country"><cfset local.strAddr[local.ATID][9] = local.qryGetAddresses.dbField></cfif>
				</cfloop>
				<cfloop collection="#local.strAddr#" item="local.thisAddr">
					<cfset local.addrName = "Address#local.thisAddr#">
					<cfset local.arrSel = arrayNew(1)>
					<cfloop array="#local.strAddr[local.thisAddr]#" index="local.addrField">
						<cfif len(local.addrField)>
							<cfset arrayAppend(local.arrSel,"+ isnull('#arguments.combineAddressDelim#' + nullIf(mockVWMD.[#local.addrField#],''),'')")>
							<cfset local.addrName = GetToken(local.addrField,1,"_")>
						</cfif>
					</cfloop>
					<cfset arrayAppend(local.arrSelect,"isnull(stuff('' #arrayToList(local.arrSel,'')#,1,#len(arguments.combineAddressDelim)#,''),'') as [mc_combinedAddress#local.thisAddr#]")>

					<cfset QueryAddRow(local.qryOutputFields)>
					<cfset QuerySetCell(local.qryOutputFields,"fieldcodeSect","mc")>
					<cfset QuerySetCell(local.qryOutputFields,"dbObject","generated")>
					<cfset QuerySetCell(local.qryOutputFields,"fieldcode","mc_combinedAddress#local.thisAddr#")>
					<cfset QuerySetCell(local.qryOutputFields,"dbField","mc_combinedAddress#local.thisAddr#")>
					<cfset QuerySetCell(local.qryOutputFields,"fieldLabel",local.addrName)>
					<cfset QuerySetCell(local.qryOutputFields,"dbObjectAlias","mc")>
				</cfloop>
			</cfif>
		</cfif>

		<!--- ----- --->
		<!--- JOINS --->
		<!--- ----- --->
		<cfset local.existingAliases = listAppend(Replace(arguments.existingAliases," ","","ALL"),"ml,grps,grpsLink,acct,mfs,mfsLink")>
		<cfset local.arrJoins = arrayNew(1)>
		<cfset local.arrJoinsNoMemberData = arrayNew(1)>
		<cfset local.arrJoinsOnlyMemberData = arrayNew(1)>
		<cfloop query="local.qryFSFields">
			<cfif listFindNoCase("vwma,vwme,vwmw,vwmpl,vwmd",local.qryFSFields.dbObjectAlias)>
				<cfset local.thisDBObjectAlias = "mockVWMD">
				<cfset local.thisDBObject = "[[mockVWTableName]]">
			<cfelse>
				<cfset local.thisDBObjectAlias = local.qryFSFields.dbObjectAlias>
				<cfset local.thisDBObject = local.qryFSFields.dbObject>
			</cfif>
			<cfif local.qryFSFields.fieldCode eq "m_recordtypeid" and not listFindNoCase(local.existingAliases,"mrt")>
				<cfset local.thisJoinString = "left outer join dbo.ams_recordTypes as mrt on mrt.recordTypeID = m.recordTypeID">
				<cfset arrayAppend(local.arrJoins,local.thisJoinString)>
				<cfset arrayAppend(local.arrJoinsOnlyMemberData,local.thisJoinString)>
				<cfset arrayAppend(local.arrJoinsNoMemberData,local.thisJoinString)>
				<cfset local.existingAliases = listAppend(local.existingAliases,"mrt")>
			<cfelseif not listFindNoCase(local.existingAliases,local.thisDBObjectAlias)>
				<cfset local.thisJoinString = "inner join #local.thisDBObject# as #local.thisDBObjectAlias# on #local.thisDBObjectAlias#.memberid = m.memberid">
				<cfset arrayAppend(local.arrJoins,local.thisJoinString)>
				<cfif local.thisDBObjectAlias eq "mockVWMD">
					<cfset arrayAppend(local.arrJoinsOnlyMemberData,local.thisJoinString)>
				<cfelse>
					<cfset arrayAppend(local.arrJoinsNoMemberData,local.thisJoinString)>
				</cfif>
				<cfset local.existingAliases = listAppend(local.existingAliases,local.thisDBObjectAlias)>
			</cfif>
		</cfloop>
		
		<cfset local.returnStruct = { 
				arrJoins = local.arrJoins,
				arrJoinsNoMemberData = local.arrJoinsNoMemberData,
				arrJoinsOnlyMemberData = local.arrJoinsOnlyMemberData,
				onlyMemberDataFieldNamesList = arrayToList(local.arrOnlyMemberDataFieldNames),
				qryOutputFields = local.qryOutputFields,
				arrSelect = local.arrSelect,
				lstAliases = local.existingAliases
			}>		
			
		<cfreturn local.returnStruct>
	</cffunction>	

	<cffunction name="makeTempTableAcceptNULLs" access="private" output="false" returntype="string">
		<cfargument name="tableName" required="true" type="string">
		
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.returnSQL">
			<cfoutput>
			/* make all cols in temp table accept nulls */
			declare @dynSQL nvarchar(max);
			declare @coltypes table (datatype varchar(16));

			insert into @coltypes 
			values ('bit'), ('binary'), ('bigint'), ('int'), ('float'), ('date'), ('datetime'), ('text'), ('image'), 
				('money'), ('uniqueidentifier'), ('smalldatetime'), ('tinyint'), ('smallint'), ('sql_variant');

			set @dynSQL = ''
			select @dynSQL = @dynSQL + 
				'ALTER TABLE ###arguments.tableName# ALTER COLUMN ' + quotename(column_name) + ' ' + Data_Type +
				case when Data_Type in (Select datatype from @coltypes) then '' else  '(' end+
				case when data_type in ('real','decimal','numeric')  then cast(isnull(numeric_precision,'') as varchar)+','+
				case when data_type in ('real','decimal','numeric') then cast(isnull(Numeric_Scale,'') as varchar) end
				when data_type in ('nvarchar','varchar') and cast(isnull(Character_Maximum_Length,'') as varchar) = '-1' then 'max'
				when data_type in ('char','nvarchar','varchar','nchar') then cast(isnull(Character_Maximum_Length,'') as varchar) else '' end+
				case when Data_Type in (Select datatype from @coltypes)then '' else  ')' end+'; '
			from tempdb.INFORMATION_SCHEMA.COLUMNS
			where table_name like '###arguments.tableName#%'
			and is_nullable = 'NO'
			order by ordinal_position;
				if len(@dynSQL) > 0 EXEC(@dynSQL);
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn local.returnSQL>
	</cffunction>	

</cfcomponent>