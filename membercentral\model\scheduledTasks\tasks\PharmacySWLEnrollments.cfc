<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<!--- These are the reports to run. No other reports should run until they are defined here. --->
	<!--- These orgcodes MUST be linked in the tblParticipants table or they will be ignored! --->
	<cfset variables.strOrgs = {
		IACP = "<EMAIL>",
		PPAG = "<EMAIL>"
	}>

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>
		<cfset local.itemCount = 0>
		
		<cfquery name="local.qryOrgs" datasource="#application.dsn.TLASITES_SEMINARWEB.dsn#">
			SELECT participantID, orgCode
			FROM dbo.tblParticipants
			WHERE orgCode IN  (<cfqueryparam value="#StructKeyList(variables.strOrgs)#" cfsqltype="CF_SQL_VARCHAR" list="yes">)
			AND isActive = 1
			ORDER BY orgCode
		</cfquery>
			
		<!--- Start date defaults to yesterday if no history found --->
		<cfset local.taskStartDate = getLastRunDate(taskCFC=arguments.strTask.taskCFC, defaultDate=dateAdd("d",-1,dateformat(now(),"m/d/yyyy")))>
		<cfset local.taskEndDate = dateAdd('d',-1,now())>
	
		<cfloop query="local.qryOrgs">
			<cftry>
				<cfset local.tmpstrOrg = { participantID=local.qryOrgs.participantID, orgcode=local.qryOrgs.orgcode }>
				<cfset local.buildEmailResult = buildEmail(strParticipant=local.tmpstrOrg, taskStartDate=local.taskStartDate, taskEndDate=local.taskEndDate)>
				<cfif local.buildEmailResult.success>
					<cfset local.itemCount = local.itemCount + local.buildEmailResult.itemCount>
				</cfif>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local.tmpstrOrg)>
			</cfcatch>
			</cftry>
		</cfloop>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>
	</cffunction>
	
	<cffunction name="buildEmail" access="private" output="false" returntype="struct">
		<cfargument name="strParticipant" type="struct" required="yes">
		<cfargument name="taskStartDate" type="date" required="yes">
		<cfargument name="taskEndDate" type="date" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix="sw")>
		<cfset local.reportFileName = "EnrollmentReport.csv">
		
		<cfstoredproc procedure="general_PharmacySWLEnrollments" datasource="#application.dsn.TLASITES_SEMINARWEB.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.strParticipant.participantID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.taskStartDate#">
			<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.taskEndDate#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\#local.reportFileName#">
			<cfprocresult name="local.qryCompleteCount" resultset="1">
			<cfprocresult name="local.qryColumns" resultset="2">
		</cfstoredproc>
			
		<cfset local.fields = valueList(local.qryColumns.COLUMN_NAME)>
		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
					
		<cfif FileExists("#local.strFolder.folderPath#/#local.reportFileName#") AND local.qryCompleteCount.returnCount gt 0>
			<cffile action="READ" file="#local.strFolder.folderPath#/#local.reportFileName#" variable="local.csvText">
			<cffile action="WRITE" addnewline="yes" file="#local.strFolder.folderPath#/#local.reportFileName#" output="#ListQualify(local.fields,chr(34))##chr(13)##chr(10)##local.csvText#" fixnewline="no">

			<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(arguments.strParticipant.orgCode)>

			<cfsavecontent variable="local.emailcontent">
				<cfoutput>
				#local.mc_siteinfo.orgname#:<br /><br />
				<cfif dateformat(arguments.taskStartDate,"m/d/yyyy") eq dateformat(arguments.taskEndDate,"m/d/yyyy")>
					Report for #dateformat(arguments.taskStartDate,"m/d/yyyy")#
				<cfelse>
					Report for #dateformat(arguments.taskStartDate,"m/d/yyyy")# - #dateformat(arguments.taskEndDate,"m/d/yyyy")#
				</cfif>
				<br /><br />
				Attached is a CSV file with webinar enrollments.<br /> 
				<br />
				If you have any questions regarding this list, please contact us.<br />
				<br />
				Thanks,<br />
				SeminarWeb Support
				</cfoutput>
			</cfsavecontent>
				
			<cfscript>
				local.arrEmailTo = [];
				
				local.toEmailArr = listToArray(variables.strOrgs[arguments.strParticipant.orgCode],';');
				for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
					local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
				}
			</cfscript>
			
			<!--- Generate download link instead of attachment --->
			<cfset local.objReportHelper = CreateObject("component","membercentral.model.reports.report")>
			<cfset local.reportDocResult = local.objReportHelper.generateScheduledReportDocument(
				siteID=local.mc_siteinfo.siteID,
				siteCode=local.mc_siteInfo.siteCode,
				orgCode=local.mc_siteInfo.orgCode,
				reportFilePath=local.strFolder.folderPath & "/" & local.reportFileName,
				reportFileName=local.reportFileName,
				reportTitle="Pharmacy SWL Enrollments Report",
				reportDescription="CSV report of SWL enrollments",
				memberID=local.mc_siteInfo.sysmemberid
			)>
			<cfif local.reportDocResult.success>
				<cfset local.emailContent = local.emailContent & local.reportDocResult.downloadHTML>
			<cfelse>
				<cfset local.emailContent = local.emailContent & "<p>Error generating download link for report.</p>">
			</cfif>

			<cfset local.strEmailResult = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name='SeminarWeb', email='<EMAIL>' },
				emailto=local.arrEmailTo,
				emailreplyto="<EMAIL>",
				emailsubject="SeminarWeb Webinar Enrollments" ,
				emailtitle="SeminarWeb Webinar Enrollments",
				emailhtmlcontent=local.emailContent,
				siteID=local.mc_siteinfo.siteID,
				memberID=local.mc_siteInfo.sysmemberid,
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SCHEDTASK"),
				sendingSiteResourceID=local.mc_siteinfo.siteSiteResourceID
			)>

			<cfset local.returnStruct.itemCount++>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>
</cfcomponent>