<cfcomponent output="false">

	<cffunction name="generateScheduledReportDocument" access="public" output="false" returnType="struct" hint="Generate report document for scheduled reports with download link">		
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="orgCode" type="string" required="true">
		<cfargument name="reportFilePath" type="string" required="true">
		<cfargument name="reportFileName" type="string" required="true">
		<cfargument name="reportTitle" type="string" required="true">
		<cfargument name="reportDescription" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="reportID" type="numeric" required="false" default="">
		<cfargument name="dateExpire" type="string" required="false" default="">

		<cfset var local = structNew()>
		<cfset local.result = { success=false, reportDocumentUID='', downloadLink='', downloadHTML='', expirationDays=7, errorMessage='' }>

		<cftry>
			<!--- Enhanced validation --->
			<cfif arguments.siteID lte 0>
				<cfset local.result.errorMessage = "Invalid siteID provided">
				<cfreturn local.result>
			</cfif>

			<cfif len(trim(arguments.siteCode)) eq 0>
				<cfset local.result.errorMessage = "siteCode cannot be empty">
				<cfreturn local.result>
			</cfif>

			<cfif len(trim(arguments.orgCode)) eq 0>
				<cfset local.result.errorMessage = "orgCode cannot be empty">
				<cfreturn local.result>
			</cfif>

			<cfif len(trim(arguments.reportFilePath)) eq 0>
				<cfset local.result.errorMessage = "reportFilePath cannot be empty">
				<cfreturn local.result>
			</cfif>

			<cfif len(trim(arguments.reportFileName)) eq 0>
				<cfset local.result.errorMessage = "reportFileName cannot be empty">
				<cfreturn local.result>
			</cfif>

			<cfif arguments.memberID lte 0>
				<cfset local.result.errorMessage = "Invalid memberID provided">
				<cfreturn local.result>
			</cfif>

			<!--- Check if report file exists --->
			<cfif not FileExists(arguments.reportFilePath)>
				<cfset local.result.errorMessage = "Report file does not exist: " & arguments.reportFilePath>
				<cfreturn local.result>
			</cfif>

			<cfquery name="local.qryGetReportSection" datasource="#application.dsn.membercentral.dsn#">
				SELECT sectionID
				FROM dbo.cms_pageSections
				WHERE siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
				AND sectionCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="MCAMSReportDocuments">
			</cfquery>

			<cfif val(local.qryGetReportSection.sectionID) is 0>
				<cfset local.result.errorMessage = "MCAMSReportDocuments section not found for siteID: " & arguments.siteID>
				<cfreturn local.result>
			</cfif>

		<cfcatch type="any">
			<cfset local.result.errorMessage = "Validation error: " & cfcatch.message & " (Detail: " & cfcatch.detail & ")">
			<cfreturn local.result>
		</cfcatch>
		</cftry>

		<cfscript>
			try {
				local.objDocument = CreateObject("component","models.system.platform.document");

				// Determine file extension from filename
				local.reportFileExt = listLast(arguments.reportFileName, ".");

				// Validate file extension
				if (len(local.reportFileExt) == 0) {
					local.result.errorMessage = "Unable to determine file extension from filename: " & arguments.reportFileName;
					return local.result;
				}

				// Create document in MCAMSReportDocuments section
				local.documentResult = local.objDocument.insertDocument(
					siteID=arguments.siteID,
					sectionID=local.qryGetReportSection.sectionID,
					resourceType="ApplicationCreatedDocument",
					docTitle=arguments.reportTitle,
					docDesc=arguments.reportDescription,
					fileName=arguments.reportFileName,
					fileExt=local.reportFileExt,
					contributorMemberID=arguments.memberID,
					recordedByMemberID=arguments.memberID
				);

				if (local.documentResult.documentID) {
					// Copy file to document storage location
					local.destinationFile = application.paths.RAIDSiteDocuments.path & arguments.orgCode & "/" & arguments.siteCode & "/" & local.documentResult.documentVersionID & ".#local.reportFileExt#";

					// Ensure destination directory exists
					local.destinationDir = getDirectoryFromPath(local.destinationFile);
					if (!DirectoryExists(local.destinationDir)) {
						DirectoryCreate(local.destinationDir, true);
					}

					fileCopy(arguments.reportFilePath, local.destinationFile);

					// Create report document record with expiration and reportID tracking
					local.reportDocumentResult = local.objDocument.insertReportDocument(
						siteID=arguments.siteID,
						documentID=local.documentResult.documentID,
						dateExpire=arguments.dateExpire,
						reportID=arguments.reportID
					);

					// Validate report document creation
					if (!structKeyExists(local.reportDocumentResult, "documentUID") || len(local.reportDocumentResult.documentUID) == 0) {
						local.result.errorMessage = "Failed to create report document record";
						return local.result;
					}

					// Calculate actual expiration days for display
					if (len(arguments.dateExpire)) {
						local.result.expirationDays = dateDiff("d", now(), arguments.dateExpire);
						// Ensure expiration days is positive
						if (local.result.expirationDays <= 0) {
							local.result.expirationDays = 1; // Minimum 1 day
						}
					} else {
						local.result.expirationDays = 7; // Default from stored procedure
					}

					// Add to S3 upload queue
					local.objDocument.addToS3UploadQueue(
						orgCode=arguments.orgCode,
						siteCode=arguments.siteCode,
						documentVersionID=local.documentResult.documentVersionID,
						fileExt=local.reportFileExt
					);

					// Generate download link and HTML with dynamic expiration
					local.result.reportDocumentUID = local.reportDocumentResult.documentUID;
					local.result.downloadLink = "https://" & application.objSiteInfo.getSiteHostName(arguments.siteCode) & "/reportDownload/" & local.reportDocumentResult.documentUID;
					local.result.downloadHTML = generateDownloadLinkHTML(local.result.downloadLink, arguments.reportFileName, "Download Report", local.result.expirationDays);
					local.result.destinationFile = local.destinationFile;
					local.result.success = true;
				} else {
					local.result.errorMessage = "Failed to create document record";
				}

			} catch (any e) {
				local.result.errorMessage = "Error processing report document: " & e.message & " (Detail: " & e.detail & ")";
			}

			return local.result;
		</cfscript>
	</cffunction>

	<cffunction name="generateDownloadLinkHTML" access="public" output="false" returnType="string" hint="Generate HTML for download link with configurable expiration notice">
		<cfargument name="downloadLink" type="string" required="true">
		<cfargument name="fileName" type="string" required="true">
		<cfargument name="linkText" type="string" required="false" default="Download Report">
		<cfargument name="expirationDays" type="numeric" required="false" default="7">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.downloadHTML">
			<cfoutput>
			<p><a href="#arguments.downloadLink#" target="_blank">#arguments.linkText#</a></p>
			<p><em>(Note: The file is only available for download within #arguments.expirationDays# day<cfif arguments.expirationDays neq 1>s</cfif>.)</em></p>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.downloadHTML>
	</cffunction>

</cfcomponent>
