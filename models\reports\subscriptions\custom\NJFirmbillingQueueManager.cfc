<cfcomponent output="false" cache="true">
	<cfproperty name="objReportHelper" inject="reports.report">
	<cfproperty name="birtRunner" inject="reports.birtRunner">

	<cffunction name="runTask" access="public" output="false" returntype="void">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structNew()>

		<cfsetting requesttimeout="400">
		
		<cfscript>
			local.arrTaskFields = [ 
				{ name="BatchSize", type="INTEGER", desc="Batch Size", value="50" },
				{ name="Threads", type="INTEGER", desc="Number of Threads", value="4" } 
			];
			local.strTaskFields = arguments.strTask.scheduledTasksUtils.setTaskCustomFields(siteID=application.objSiteInfo.mc_siteinfo['MC'].siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>	

		<cfset local.messageTypeID = arguments.strTask.scheduledTasksUtils.getMessageTypeID(messageTypeCode="NJFIRMBILLRPT")>
		<cfset local.processQueueResult = processQueue(messageTypeID=local.messageTypeID, batchSize=local.strTaskFields.batchSize, threads=local.strTaskFields.threads)>
		<cfif NOT local.processQueueResult.success>
			<cfthrow message="Error running processQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier='', itemcount=local.processQueueResult.itemCount)>
		</cfif>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfargument name="messageTypeID" type="numeric" required="true">
		<cfargument name="batchSize" type="numeric" required="true">
		<cfargument name="threads" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset var reportPath = "/app/models/reports/members/njfirmbilling.rptdesign">
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>

		<cftry>
			<cfstoredproc procedure="queue_NJFirmSubStatements_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#application.paths.SharedTemp.pathUNC#firm_">
				<cfprocresult name="local.qryFirms" resultset="1">
			</cfstoredproc>

			<cfset local.returnStruct.itemCount += local.qryFirms.recordCount>

			<cfscript>
				// loop per firm
				QueryEach(local.qryFirms, function(struct thisFirm, numeric currentrow, query qryFirms) {

					var outputFileName = "";
					var reportXMLFile = "";
					var paramSet = arrayNew(1);
					var reportParams = arrayNew(1);
					var success = false;

					// item must still be in the grabbedForProcessing state for this job. else skip it. --->
					//this is here because we have a check in place that will attempt to clear items "stuck" in the grabbedForProcessing state and those items may be grabbed by another job, causing it to possible be processed twice. --->
					if (queueItemHasStatus(itemID=arguments.thisFirm.itemID, queueStatus='grabbedForProcessing')) {
						try {
							<!--- UPDATE STATUS --->
							queueSetStatus(itemID=arguments.thisFirm.itemID, queueStatus='processingFirm');
							// filename for the pdf
							outputFileName = "#arguments.thisFirm.firmCompany#_#arguments.thisFirm.firmFirstName#_#arguments.thisFirm.firmLastName#_#arguments.thisFirm.firmMemberNumber#";
							outputFileName = replace(replace(rereplaceNoCase(outputFileName,'[^A-Z0-9]','','ALL'),' ','_','ALL'),'__','_','ALL');
							outputFileName = "#application.paths.SharedTemp.path#firm_#arguments.thisFirm.itemGroupUID#/#outputFileName#.pdf" ;
							reportXMLFile = "#application.paths.SharedTemp.path#firm_#arguments.thisFirm.itemGroupUID#/#arguments.thisFirm.itemID#.xml" ;

							arrayAppend(reportParams,{ name="xmlFilePath", value=reportXMLFile, datatype="filepath", needsEvaluation=false });

							paramSet = XMLSearch(arguments.thisFirm.xmlConfigParam,'/params/param');
							for (var thisParam in paramSet) {
								arrayAppend(reportParams,{
									name=thisParam.reportParam.xmlText,
									value=thisParam.paramvalue.xmlText,
									datatype="string",
									needsEvaluation=false
								});
							}
							success = birtRunner.runReport(reportPath=reportPath, renderFormat="pdf", destinationPath=outputFileName, reportParams=reportParams);

							// UPDATE STATUS
							if (success) 
								queueSetStatus(itemID=arguments.thisFirm.itemID, queueStatus='readyToNotify');

						} catch (e) {
							application.objError.sendError(cfcatch=e, objectToDump=local);
							rethrow;
						}
					}
				}, true, arguments.threads);
			</cfscript>

			<!--- ----------------------------------------------------------------------------------- --->
			<!--- post processing: process any notifications for itemGroupUIDs that are readyToNotify --->
			<!--- ----------------------------------------------------------------------------------- --->
			<cftry>
				<cfquery name="local.qryNotifications" datasource="#application.dsn.platformQueue.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						EXEC dbo.queue_NJFirmSubStatements_grabForNotification;
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

				<cfset local.returnStruct.itemCount += local.qryNotifications.recordCount>

				<cfif local.qryNotifications.recordcount>
					<cfloop query="local.qryNotifications">
						<cftry>
							<cfset local.directoryPath = "#application.paths.SharedTemp.path#firm_#local.qryNotifications.itemGroupUID#">
							
							<!--- merge pdfs --->
							<cfset local.reportFileName = "FirmStatements.pdf">
							<cfset local.reportFileDesc = "PDF of Firm Billing Statements">
							<cfdirectory action="list" directory="#local.directoryPath#" name="local.qryPDFs" filter="*.pdf" recurse="false" sort="name">
							<cfif local.qryPDFs.recordcount>
								<cfset application.objCommon.mergePDFs(pdfPath=local.directoryPath, listofPDFs=valuelist(local.qryPDFs.name), finalOutPutFile=local.reportFileName)>
							</cfif>

							<!--- record site document --->
							<cfset local.documentResult = objReportHelper.generateReportDocument(reportID=local.qryNotifications.reportID, reportTitle="Firm Billing Statements",
								siteID=local.qryNotifications.siteID, orgCode=local.qryNotifications.orgCode, siteCode=local.qryNotifications.siteCode, directoryPath=local.directoryPath, 
								reportFileName=local.reportFileName, memberID=local.qryNotifications.memberID)>
							<cfif not local.documentResult.success>
								<cfthrow message="#local.documentResult.errorMessage#">
							</cfif>					

							<!--- prep and send email --->
							<cfset local.thisReportEmail = local.qryNotifications.reportEmail>
							<cfset local.thisSiteName = local.qryNotifications.siteName>
							<cfset local.thisSiteCode = local.qryNotifications.siteCode>

							<cfif len(local.thisReportEmail)>
								<cfsavecontent variable="local.thisEmailContent">
									<cfoutput>
									<div>
										#local.qryNotifications.firstname#:<br/><br/>
										We have completed generating your requested Firm Billing Statements.<br/><br/>
									</div>
									#local.documentResult.downloadHTML#
									</cfoutput>
								</cfsavecontent>

								<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
									emailfrom={ name="MemberCentral", email=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].networkEmailFrom},
									emailto=[{ name="#local.qryNotifications.firstname# #local.qryNotifications.lastname#", email=local.thisReportEmail }],
									emailreplyto=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].supportProviderEmail,
									emailsubject="Firm Billing Statements for #local.thisSiteName#",
									emailtitle="#local.thisSiteName# Firm Billing Statements",
									emailhtmlcontent=local.thisEmailContent,
									siteID=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].siteID,
									memberID=val(local.qryNotifications.memberID),
									messageTypeID=arguments.messageTypeID,
									sendingSiteResourceID=application.objSiteInfo.mc_siteInfo[local.thisSiteCode].siteSiteResourceID
									)>
							</cfif>

							<!--- update status --->
							<cfquery name="local.updateStatusInMass" datasource="#application.dsn.platformQueue.dsn#">
								SET NOCOUNT ON;

								DECLARE @doneStatusID INT;
								EXEC dbo.queue_getStatusIDbyType @queueType='NJFirmSubStatements', @queueStatus='done', @queueStatusID=@doneStatusID OUTPUT;

								UPDATE dbo.queue_NJFirmSubStatements
								SET statusID = @doneStatusID,
									dateUpdated = GETDATE()
								WHERE itemGroupUID = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#local.qryNotifications.itemGroupUID#">;
							</cfquery>

						<cfcatch type="Any">
							<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
						</cfcatch>
						</cftry>

					</cfloop>
				</cfif>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>			

			<!--- -------------------------------------------------------- --->
			<!--- post processing - delete any itemGroupUIDs that are done --->
			<!--- -------------------------------------------------------- --->
			<cftry>
				<cfquery name="local.qryClear" datasource="#application.dsn.platformQueue.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						EXEC dbo.queue_NJFirmSubStatements_clearDone;
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>	

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>
					
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="queueSetStatus" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="string" required="true">
		<cfargument name="queueStatus" type="string" required="true">

		<cfset var qryUpdateQueueStatus = "">

		<cfquery name="qryUpdateQueueStatus" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			
			DECLARE @statusID int;
			EXEC dbo.queue_getStatusIDbyType @queueType='NJFirmSubStatements', @queueStatus=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.queueStatus#">, @queueStatusID=@statusID OUTPUT;

			UPDATE dbo.queue_NJFirmSubStatements
			SET statusID = @statusID,
				dateUpdated = GETDATE()
			WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">;
		</cfquery>
	</cffunction>

	<cffunction name="queueItemHasStatus" access="private" output="false" returntype="boolean">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="queueStatus" type="string" required="true">

		<cfset var qryCheckItem = "">

		<cfquery name="qryCheckItem" datasource="#application.dsn.platformQueue.dsn#">
			SELECT count(qi.itemID) AS itemCount
			FROM dbo.queue_NJFirmSubStatements AS qi
			INNER JOIN dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID 
			WHERE qi.itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">
			AND qs.queueStatus = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.queueStatus#">
		</cfquery>

		<cfreturn (qryCheckItem.itemCount GT 0)>
	</cffunction>
</cfcomponent>